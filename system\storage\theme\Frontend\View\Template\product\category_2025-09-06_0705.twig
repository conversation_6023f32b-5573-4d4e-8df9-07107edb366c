{# Шаблон: Категория продукти #}
<div class="min-h-screen pt-6 pb-12">
  <div class="container mx-auto px-4">
    {# Breadcrumbs #}
    {% if breadcrumbs %}
      <div class="mb-4">
        <ul class="flex text-sm text-gray-500">
          {% for bc in breadcrumbs %}
            <li class="flex items-center">
              {% if bc.href %}<a href="{{ bc.href }}" class="hover:text-primary">{{ bc.text }}</a>{% else %}<span>{{ bc.text }}</span>{% endif %}
              {% if not loop.last %}<i class="ri-arrow-right-s-line mx-2"></i>{% endif %}
            </li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}

    {# Заглавие и описание #}
    <div class="mb-6">
      <h1 class="text-3xl font-bold">{{ category_title }}</h1>
      {% if category_description %}
        <div class="text-gray-600 mt-2">{{ category_description|raw }}</div>
      {% endif %}
    </div>

    <div class="flex flex-col lg:flex-row gap-8">
      {# Left Sidebar: Филтри (
         Заб.: функционалността за AJAX ще бъде добавена в отделен JS модул и/или суб-контролер).
      #}
      <aside class="lg:w-1/4 w-full">
        <div id="js-category-filters" class="bg-white rounded-lg shadow-md p-6 sticky top-32">
          <div class="mb-6">
            {% if has_active_filters %}
            <a href="{{ clear_filters_url }}" class="w-full bg-primary text-white py-2 px-4 rounded-button hover:bg-opacity-90 block text-center">
              Изчисти филтрите
            </a>
            {% endif %}
          </div>

          {# Цена #}
          <div class="mb-6">
            <div class="flex justify-between items-center mb-4 cursor-pointer">
              <h3 class="font-semibold text-lg">Цена</h3>
            </div>
            <div>
              <div class="flex gap-4">
                <div class="flex-1">
                  <label class="text-sm text-gray-600 mb-1 block">От</label>
                  <input type="number" class="w-full border border-gray-300 rounded p-2 text-sm" name="price_min" value="{{ price_min|default('') }}">
                </div>
                <div class="flex-1">
                  <label class="text-sm text-gray-600 mb-1 block">До</label>
                  <input type="number" class="w-full border border-gray-300 rounded p-2 text-sm" name="price_max" value="{{ price_max|default('') }}">
                </div>
              </div>
            </div>
          </div>

          {# Наличност #}
          <div class="mb-6">
            <div class="flex justify-between items-center mb-4 cursor-pointer">
              <h3 class="font-semibold text-lg">Наличност</h3>
            </div>
            <div class="space-y-3">
              <label class="custom-checkbox flex items-center">
                <input type="checkbox" name="in_stock" {% if in_stock %}checked{% endif %}>
                <span class="checkmark"></span>
                <span class="ml-2">В наличност</span>
              </label>
            </div>
          </div>

          {# Производител #}
          {% if manufacturers and manufacturers|length %}
          <div class="mb-6">
            <div class="flex justify-between items-center mb-4 cursor-pointer">
              <h3 class="font-semibold text-lg">Производител</h3>
            </div>
            <div class="space-y-2 max-h-64 overflow-auto pr-1">
              {% for m in manufacturers %}
              <label class="custom-checkbox flex items-center">
                <input type="checkbox" name="manufacturer_id[]" value="{{ m.manufacturer_id }}">
                <span class="checkmark"></span>
                <span class="ml-2">{{ m.name }}</span>
              </label>
              {% endfor %}
            </div>
          </div>
          {% endif %}

        </div>
      </aside>

      {# Product grid + Controls #}
      <section class="lg:w-3/4 w-full">
        <div class="bg-gray-50 px-3 py-4 rounded-lg mb-6 flex flex-col sm:flex-row justify-between items-center">
          <div class="flex items-center mb-4 sm:mb-0 gap-3">
            <span class="text-gray-600">Сортирай по:</span>
            <select id="js-sort-select" class="border border-gray-300 rounded-lg py-2 px-3">
              {% for s in sorts %}
                <option value="{{ s.key }}" {% if current_sort == s.key %}selected{% endif %}>{{ s.text }}</option>
              {% endfor %}
            </select>
            <select id="js-order-select" class="border border-gray-300 rounded-lg py-2 px-3">
              <option value="ASC" {% if current_order=='ASC' %}selected{% endif %}>Възходящо</option>
              <option value="DESC" {% if current_order=='DESC' %}selected{% endif %}>Низходящо</option>
            </select>
          </div>
          <div class="flex items-center gap-3">
            <span class="text-gray-600">Покажи:</span>
            <select id="js-limit-select" class="border border-gray-300 rounded-lg py-2 px-3">
              {% for l in limits %}
                <option value="{{ l.value }}" {% if current_limit == l.value %}selected{% endif %}>{{ l.text }}</option>
              {% endfor %}
            </select>
          </div>
        </div>

        <div id="js-category-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {% for p in products %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:scale-105">
              <a href="{{ p.href }}" class="block">
                <div class="relative overflow-hidden">
                  {% if p.thumb %}
                    <img src="{{ p.thumb }}" alt="{{ p.name }}" class="w-full h-64 object-cover object-top" loading="lazy">
                  {% endif %}
                  {% if p.labels and p.labels.special %}
                    <div class="absolute top-2 left-2 bg-primary text-white text-sm px-2 py-1 rounded">-{{ p.labels.special.percent }}%</div>
                  {% endif %}
                </div>
                <div class="p-4">
                  <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">{{ p.name }}</h3>
                  <div class="flex justify-between items-center">
                    <div class="flex flex-col">
                      <span class="text-primary font-bold text-xl">{{ p.special is not null ? p.special_formatted : p.price_formatted }}</span>
                      {% if p.special is not null %}
                        <span class="text-gray-400 line-through text-sm">{{ p.price_formatted }}</span>
                      {% endif %}
                    </div>
                    <button data-product-id="{{ p.product_id }}" class="bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap js-add-to-cart">Купи</button>
                  </div>
                </div>
              </a>
            </div>
          {% else %}
            <div class="col-span-3 text-center text-gray-500 py-10">Няма продукти в тази категория.</div>
          {% endfor %}
        </div>

        {% if pagination %}
          <div id="js-category-pagination" class="mt-10">{{ pagination|raw }}</div>
        {% endif %}

        {% if json_ld %}
          <script type="application/ld+json">{{ json_ld|raw }}</script>
        {% endif %}
      </section>
    </div>
  </div>
</div>
