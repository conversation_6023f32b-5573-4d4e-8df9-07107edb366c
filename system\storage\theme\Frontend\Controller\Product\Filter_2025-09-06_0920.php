<?php

namespace Theme25\Frontend\Controller\Product;

/**
 * AJAX контролер за филтриране на продукти (route: product/filter)
 */
class Filter extends Cards
{
    public function __construct($registry)
    {
        parent::__construct($registry, 'product/filter');
        if (\is_callable([$this, 'loadScripts'])) {
            $this->loadScripts(['cart']);
        }
    }

    public function index()
    {
        // Само AJAX заявки
        if (\is_callable([$this, 'isAjaxRequest']) && !$this->isAjaxRequest()) {
            return $this->jsonResponse(['success' => false, 'error' => 'Невалидна заявка']);
        }

        // Параметри
        $category_id = $this->getCategoryIdFromRequest();
        if ($category_id <= 0) {
            return $this->jsonResponse(['success' => false, 'error' => 'Невалидна категория']);
        }

        $page  = $this->requestGet('page') ? max(1, (int)$this->requestGet('page')) : 1;
        $limit = $this->getLimit();
        list($sort, $order) = $this->getSortAndOrder();

        // Филтрите от заявката
        $filters = [
            'price_min' => $this->requestGet('price_min'),
            'price_max' => $this->requestGet('price_max'),
            'manufacturer_id' => $this->requestGet('manufacturer_id'), // може да е CSV
            'in_stock' => $this->requestGet('in_stock') ? 1 : 0,
            // TODO: attributes, options, rating
        ];

        // Модели
        $this->loadModelsAs([
            'catalog/product' => 'productModel',
            'catalog/product/filter' => 'productFilterModel',
        ]);

        // Намиране на продукти
        $offset = ($page - 1) * $limit;
        $result = $this->productFilterModel->findProductsByCategory($category_id, $filters, $sort, $order, $limit, $offset);
        $product_ids = $result['product_ids'] ?? [];
        $total       = (int)($result['total'] ?? 0);

        // Подготовка на карти
        $products = [];
        if ($product_ids) {
            $this->loadModelAs('tool/image', 'imageModel');
            foreach ($product_ids as $pid) {
                $info = $this->productModel->getProduct((int)$pid);
                if (!$info) continue;
                $products[] = $this->prepareProductCardData($info, false);
            }
        }

        // Пагинация HTML
        $pagination_html = $this->buildPagination($total, $page, $limit);

        // Филтърни опции (за обновяване на UI при нужда)
        $filter_options = $this->productFilterModel->getFilterOptions($category_id);

        return $this->jsonResponse([
            'success' => true,
            'products' => $products,
            'total' => $total,
            'pagination' => $pagination_html,
            'filters' => $filter_options,
        ]);
    }

    private function getCategoryIdFromRequest(): int
    {
        $path = (string)($this->requestGet('path') ?: '');
        if ($path !== '') {
            $parts = explode('_', $path); $last = end($parts); return ctype_digit((string)$last) ? (int)$last : 0;
        }
        return (int)$this->requestGet('category_id');
    }

    private function getSortAndOrder(): array
    {
        $allowedSorts = [
            'default'    => 'p.sort_order',
            'name'       => 'pd.name',
            'price'      => 'p.price',
            'date_added' => 'p.date_added',
            'rating'     => 'rating',
            'popularity' => 'p.viewed',
        ];
        $sortKey = $this->requestGet('sort') ?: 'default';
        $order   = strtoupper($this->requestGet('order') ?: 'ASC');
        if (!isset($allowedSorts[$sortKey])) $sortKey = 'default';
        if (!in_array($order, ['ASC', 'DESC'], true)) $order = 'ASC';
        return [$allowedSorts[$sortKey], $order];
    }

    private function buildPagination(int $total, int $page, int $limit): string
    {
        if ($total <= $limit) return '';
        $pagination = new \Theme25\Pagination();
        $pagination->total = $total; $pagination->page = $page; $pagination->limit = $limit;
        $base = $this->getLink('product/category');
        $qs = $this->getPreservedQueryString(['page']);
        $url = $base . ($qs ? '&' . $qs : '');
        $pagination->url = $url . '&page={page}';
        $pagination->setLimits([
            ['value'=>12,'text'=>'12 на страница'],
            ['value'=>24,'text'=>'24 на страница'],
            ['value'=>36,'text'=>'36 на страница'],
        ]);
        $pagination->setLimitUrl($url . '&limit={limit}');
        $pagination->setProductText('продукта');
        return $pagination->render();
    }

    private function getPreservedQueryString(array $exclude = []): string
    {
        $params = [];
        foreach ((array)$_GET as $k=>$v) {
            if (in_array($k, $exclude, true)) continue; if ($v===''||$v===null) continue;
            $params[] = rawurlencode($k).'='.rawurlencode((string)$v);
        }
        return implode('&',$params);
    }

    private function getLimit(): int
    {
        $limit = (int)($this->requestGet('limit') ?: 12);
        if (!in_array($limit, [12, 24, 36], true)) {
            $limit = 12;
        }
        return $limit;
    }
}

