<?php

namespace Theme25\Frontend\Controller\Product;

/**
 * Контролер за листинг на продукти по категория (route: product/category)
 */
class Category extends Cards
{
    public function __construct($registry)
    {
        parent::__construct($registry, 'product/category');
    }

    public function index()
    {
        $this->addFrontendScriptWithVersion('product-category.js', 'footer');

        // 1) Валидация и извличане на category_id от URL (path)
        $category_id = $this->getCategoryIdFromRequest();
        if ($category_id <= 0) {
            return $this->renderNotFound();
        }

        // 2) Зареждане на нужните модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'catalog/product'  => 'productModel',
            'tool/image'       => 'imageModel'
        ]);

        // 3) Проверка дали категорията съществува и е активна
        $category_info = $this->categoryModel->getCategory($category_id);
        if (!$category_info || (isset($category_info['status']) && (int)$category_info['status'] !== 1)) {
            return $this->renderNotFound();
        }

        // 4) SEO заглавия и мета
        $title = $category_info['meta_title'] ?? $category_info['name'] ?? 'Категория';
        $this->document->setTitle($title);
        if (!empty($category_info['meta_description'])) {
            $this->document->setDescription($category_info['meta_description']);
        }
        if (!empty($category_info['meta_keyword'])) {
            $this->document->setKeywords($category_info['meta_keyword']);
        }

        // 5) Breadcrumbs от path
        $breadcrumbs = $this->buildBreadcrumbs($category_id);

        // 6) Сортиране, лимит и страница
        list($sort, $order, $sortKey) = $this->getSortAndOrder();
        $limit = $this->getLimit(); // 10/20/50/100 (по подразбиране 20)
        $page  = $this->requestGet('page') ? max(1, (int)$this->requestGet('page')) : 1;
        $start = ($page - 1) * $limit;

        // 7) Филтърни параметри от URL
        $filters = [
            'price_min' => $this->requestGet('price_min'),
            'price_max' => $this->requestGet('price_max'),
            'manufacturer_id' => $this->requestGet('manufacturer_id'), // може да е CSV
            'in_stock' => $this->requestGet('in_stock') ? 1 : 0,
        ];

        // 8) Използваме специализирания филтърен модел
        $this->loadModelAs('catalog/product/filter', 'productFilterModel');
        $offset = ($page - 1) * $limit;
        $result = $this->productFilterModel->findProductsByCategory($category_id, $filters, $sort, $order, $limit, $offset);
        $product_ids = $result['product_ids'] ?? [];
        $total_results = (int)($result['total'] ?? 0);

        // 9) Подготовка на карти
        $products = [];
        if ($product_ids) {
            $this->loadModelAs('tool/image', 'imageModel');
            foreach ($product_ids as $pid) {
                $info = $this->productModel->getProduct((int)$pid);
                if (!$info) continue;
                $products[] = $this->prepareProductCardData($info, false);
            }
        }

        // 10) Пагинация с \\Theme25\\Pagination и запазване на всички query параметри
        $pagination_html = $this->buildPagination($total_results, $page, $limit);

        // 10.1) Филтърни опции за UI
        $this->loadModelAs('catalog/product/filter', 'productFilterModel');
        $filterOptions = $this->productFilterModel->getFilterOptions($category_id);

        // 10.1.1) Price range за slider
        $priceRange = $this->productFilterModel->getPriceRange($category_id);
        $minPrice = isset($priceRange['min']) ? (int)$priceRange['min'] : 0;
        $maxPrice = isset($priceRange['max']) ? (int)$priceRange['max'] : 1000;

        // 10.2) Активни филтри (за бутон "Изчисти филтрите")
        $g = isset($_GET) && is_array($_GET) ? $_GET : [];
        $has_active_filters = false;
        foreach (['price_min','price_max','manufacturer_id'] as $k) {
            if (isset($g[$k]) && $g[$k] !== '' && $g[$k] !== null) { $has_active_filters = true; break; }
        }
        if (!$has_active_filters && isset($g['in_stock']) && (string)$g['in_stock'] === '1') { $has_active_filters = true; }

        // 11) Данни към шаблона
        $this->data['category_title']       = $category_info['name'] ?? '';
        // Описанието се показва само на първата страница
        $this->data['category_description'] = ($page == 1 && $category_info['description']) ? html_entity_decode($category_info['description']) : '';
        $this->data['breadcrumbs']          = $breadcrumbs;
        $this->data['products']             = $products;
        $this->data['total_results']        = (int)$total_results;
        $this->data['pagination']           = $pagination_html;
        $this->data['current_page']         = $page;
        $this->data['current_limit']        = $limit;
        $this->data['limits']               = $this->getLimitOptions();
        $this->data['sorts']                = $this->getSortOptions($sort, $order);
        $this->data['current_sort']         = $sortKey; // Използваме ключа вместо SQL полето
        $this->data['current_order']        = $order;

        // 11.1) Филтър опции към UI
        $this->data['filters']               = $filterOptions;
        $this->data['manufacturers']         = isset($filterOptions['manufacturers']) ? $filterOptions['manufacturers'] : [];
        $this->data['price_min_available']   = isset($filterOptions['price']['min']) ? (float)$filterOptions['price']['min'] : 0.0;
        $this->data['price_max_available']   = isset($filterOptions['price']['max']) ? (float)$filterOptions['price']['max'] : 0.0;

        // 12) Structured Data (JSON-LD) – basic ProductList
        $this->data['json_ld'] = $this->buildJsonLd($products, $category_info);

        // URL помощници за шаблона - премахваме всички филтърни параметри
        $this->data['clear_filters_url'] = $this->buildUrlWithOverrides([
            'page' => null, 'limit' => null, 'sort' => null, 'order' => null,
            'price_min' => null, 'price_max' => null, 'manufacturer_id' => null, 'in_stock' => null
        ]);
        // За JS селектите подаваме само стойности, не URL-и
        $this->data['order_urls'] = [ 'ASC' => 'ASC', 'DESC' => 'DESC' ];
        $this->data['limit_urls'] = [ 10 => 10, 20 => 20, 50 => 50, 100 => 100 ];
        // Флаг за активни филтри + текущи стойности
        $this->data['has_active_filters'] = $has_active_filters;
        $this->data['price_min'] = isset($g['price_min']) ? (string)$g['price_min'] : '';
        $this->data['price_max'] = isset($g['price_max']) ? (string)$g['price_max'] : '';
        $this->data['in_stock']  = isset($g['in_stock']) && (string)$g['in_stock'] === '1' ? 1 : 0;

        // Price range за slider
        $this->data['price_range_min'] = $minPrice;
        $this->data['price_range_max'] = $maxPrice;

        // Валутни настройки за JavaScript
        $this->data['currency_config'] = $this->getCurrencyConfig();

        return $this->renderTemplateWithDataAndOutput('product/category');
    }

    private function buildUrlWithOverrides(array $overrides): string
    {
        // Използваме текущия SEO-friendly URL като база
        $current_url = $_SERVER['REQUEST_URI'];
        $url_parts = parse_url($current_url);
        $base_url = $url_parts['path']; // Само пътя без query параметри

        $params = [];
        foreach ((array)$_GET as $k => $v) {
            if ($v === '' || $v === null) continue;
            $params[$k] = (string)$v;
        }
        foreach ($overrides as $k => $v) {
            if ($v === null) {
                unset($params[$k]);
            } else {
                $params[$k] = (string)$v;
            }
        }
        $query = http_build_query($params);
        return $query ? ($base_url . '?' . $query) : $base_url;
    }

    private function buildSortUrls(): array
    {
        $urls = [];
        foreach (['default','name','price','date_added','popularity','rating'] as $key) {
            $urls[$key] = $this->buildUrlWithOverrides(['sort' => $key, 'page' => null]);
        }
        return $urls;
    }

    private function buildLimitUrls(): array
    {
        $urls = [];
        foreach ([10,20,50,100] as $val) {
            $urls[$val] = $this->buildUrlWithOverrides(['limit' => (string)$val, 'page' => null]);
        }
        return $urls;
    }

    private function renderNotFound()
    {
        // Може да се използва централен 404 контролер при нужда
        $this->document->setTitle('Категория не е намерена');
        $this->setData([
            'category_title' => 'Категория не е намерена',
            'category_description' => ''
        ]);
        return $this->renderTemplateWithDataAndOutput('error/under_construction');
    }

    private function getCategoryIdFromRequest(): int
    {
        // OpenCart обичайно подава path=10_20_30 (последната е текущата)
        $path = (string)($this->requestGet('path') ?: '');
        if ($path === '') return (int)$this->requestGet('category_id'); // fallback
        $parts = explode('_', $path);
        $last  = end($parts);
        return ctype_digit((string)$last) ? (int)$last : 0;
    }

    private function buildBreadcrumbs(int $category_id): array
    {
        $breadcrumbs = [];
        // Начало
        $breadcrumbs[] = [
            'text' => 'Начало',
            'href' => $this->getLink('common/home')
        ];

        // Ако имаме path, запазваме целия и генерираме линкове
        $path = (string)($this->requestGet('path') ?: '');
        if ($path !== '') {
            $accum = [];
            foreach (explode('_', $path) as $cid) {
                if (!ctype_digit((string)$cid)) continue;
                $accum[] = $cid;
                $info = $this->categoryModel->getCategory((int)$cid);
                if (!$info) continue;
                $breadcrumbs[] = [
                    'text' => $info['name'] ?? ('Категория ' . (int)$cid),
                    'href' => $this->getLink('product/category', 'path=' . implode('_', $accum))
                ];
            }
        } else {
            // Без path – поне текущата категория
            $info = $this->categoryModel->getCategory($category_id);
            if ($info) {
                $breadcrumbs[] = [
                    'text' => $info['name'] ?? 'Категория',
                    'href' => $this->getLink('product/category', 'category_id=' . $category_id)
                ];
            }
        }

        return $breadcrumbs;
    }

    private function getSortAndOrder(): array
    {
        $allowedSorts = [
            'default'      => 'p.sort_order',
            'name'         => 'pd.name',
            'price'        => 'p.price',
            'date_added'   => 'p.date_added',
            'rating'       => 'rating',
            'popularity'   => 'p.viewed',
        ];

        $sortKey = $this->requestGet('sort') ?: 'default';
        $order   = strtoupper($this->requestGet('order') ?: 'ASC');
        if (!isset($allowedSorts[$sortKey])) {
            $sortKey = 'default';
        }
        if (!in_array($order, ['ASC', 'DESC'], true)) {
            $order = 'ASC';
        }
        return [$allowedSorts[$sortKey], $order, $sortKey]; // Връщаме и ключа
    }

    private function getLimit(): int
    {
        $limit = (int)($this->requestGet('limit') ?: 12);
        if (!in_array($limit, [12, 24, 36], true)) {
            $limit = 12;
        }
        return $limit;
    }

    private function getLimitOptions(): array
    {
        return [
            ['value' => 12,  'text' => '12 на страница'],
            ['value' => 24,  'text' => '24 на страница'],
            ['value' => 36,  'text' => '36 на страница'],
        ];
    }

    private function getSortOptions(string $currentSort, string $currentOrder): array
    {
        // Връщаме конфигурации за UI
        return [
            ['key' => 'default',    'text' => 'По подразбиране'],
            ['key' => 'name',       'text' => 'Име'],
            ['key' => 'price',      'text' => 'Цена'],
            ['key' => 'date_added', 'text' => 'Най-нови'],
            ['key' => 'popularity', 'text' => 'Популярност'],
            ['key' => 'rating',     'text' => 'Рейтинг'],
        ];
    }

    private function buildPagination(int $total, int $page, int $limit): string
    {
        if ($total <= $limit) return '';

        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page  = $page;
        $pagination->limit = $limit;

        // Използваме текущия SEO-friendly URL като база
        $current_url = $_SERVER['REQUEST_URI'];
        $url_parts = parse_url($current_url);
        $base_url = $url_parts['path']; // Само пътя без query параметри

        // Запазваме всички текущи query параметри (без page)
        $qs = $this->getPreservedQueryString(['page','_route_']);
        $url = $base_url . ($qs ? '?' . $qs : '');
        $pagination->url = $url . ($qs ? '&' : '?') . 'page={page}';
        $pagination->setLimits($this->getLimitOptions());
        $pagination->setLimitUrl($url . ($qs ? '&' : '?') . 'limit={limit}');
        $pagination->setProductText('продукта');

        return $pagination->render();
    }

    private function getPreservedQueryString(array $exclude = []): string
    {
        $params = [];
        if (!empty($_GET)) {
            foreach ($_GET as $k => $v) {
                if (in_array($k, $exclude, true)) continue;
                if ($v === '' || $v === null) continue;
                $params[] = rawurlencode($k) . '=' . rawurlencode((string)$v);
            }
        }
        return implode('&', $params);
    }

    private function buildJsonLd(array $products, array $category_info): string
    {
        $items = [];
        foreach ($products as $p) {
            $items[] = [
                '@type' => 'Product',
                'name'  => $p['name'] ?? '',
                'url'   => $p['href'] ?? '',
                'image' => $p['thumb'] ?? '',
                'offers' => [
                    '@type' => 'Offer',
                    'priceCurrency' => $this->getConfig('config_currency'),
                    'price' => isset($p['special']) && $p['special'] !== null ? (float)$p['special'] : (float)$p['price'],
                    'availability' => 'http://schema.org/InStock'
                ]
            ];
        }

        $data = [
            '@context' => 'http://schema.org',
            '@type'    => 'CollectionPage',
            'name'     => $category_info['name'] ?? 'Категория',
            'hasPart'  => $items,
        ];

        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Получава валутната конфигурация за JavaScript
     */
    private function getCurrencyConfig(): array
    {
        // Получаване на настройката за формат на изписване на цените
        $priceDisplayFormat = 'bgn'; // Default
        if (is_callable([$this, 'getConfig'])) {
            $priceDisplayFormat = $this->getConfig('config_price_display_format', 'bgn');
        }

        // Използваме фиксирания курс от Currency класа
        $bgnToEurRate = 1.95583; // 1 EUR = 1.95583 BGN
        $eurToBgnRate = 1 / $bgnToEurRate; // 1 BGN = 0.5113 EUR

        // Валутни данни
        $currencies = [
            'bgn' => [
                'code' => 'BGN',
                'title' => 'Български лев',
                'symbol_left' => '',
                'symbol_right' => ' лв.',
                'decimal_place' => 2,
                'value' => 1.0000 // Базова валута
            ],
            'eur' => [
                'code' => 'EUR',
                'title' => 'Евро',
                'symbol_left' => '',
                'symbol_right' => ' €',
                'decimal_place' => 2,
                'value' => $eurToBgnRate // 1 BGN = 0.5113 EUR
            ]
        ];

        return [
            'display_format' => $priceDisplayFormat,
            'bgn' => $currencies['bgn'],
            'eur' => $currencies['eur']
        ];
    }
}

