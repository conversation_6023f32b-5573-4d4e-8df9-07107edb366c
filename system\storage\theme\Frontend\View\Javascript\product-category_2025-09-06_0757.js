(function(){
  'use strict';
  // Флаг за еднократна инициализация на custom dropdown-и
  var ddInitialized = false;


  function serializeFilters(container){
    // Започваме от текущите параметри, но ще синхронизираме с UI стойностите
    var params = new URLSearchParams(window.location.search);
    var priceMin = container.querySelector('input[name="price_min"]');
    var priceMax = container.querySelector('input[name="price_max"]');
    var inStock  = container.querySelector('input[name="in_stock"]');
    var manufEls = container.querySelectorAll('input[name="manufacturer_id[]"]');
    var sortSel  = document.getElementById('js-sort-select');
    var orderSel = document.getElementById('js-order-select');
    var limitSel = document.getElementById('js-limit-select');

    if (priceMin && priceMin.value) params.set('price_min', priceMin.value); else params.delete('price_min');
    if (priceMax && priceMax.value) params.set('price_max', priceMax.value); else params.delete('price_max');
    if (inStock && inStock.checked) params.set('in_stock', '1'); else params.delete('in_stock');

    var manuf = [];
    manufEls.forEach(function(el){ if (el.checked) manuf.push(el.value); });
    if (manuf.length) params.set('manufacturer_id', manuf.join(',')); else params.delete('manufacturer_id');

    if (sortSel && sortSel.value) params.set('sort', sortSel.value); else params.delete('sort');
    if (orderSel && orderSel.value) params.set('order', orderSel.value); else params.delete('order');
    if (limitSel && limitSel.value) params.set('limit', limitSel.value); else params.delete('limit');

    // reset page когато филтрите се сменят
    params.delete('page');
    return params;
  }

  function updateGrid(json){
    var grid = document.getElementById('js-category-grid');
    var pag  = document.getElementById('js-category-pagination');
    if (!grid) return;

    if (!json || !json.success){ return; }

    // Build cards
    var html = '';
    if (json.products && json.products.length){
      json.products.forEach(function(p){
        html += '\n<div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:scale-105">' +
                '  <a href="'+ (p.href||'#') +'" class="block group" data-product-id="'+p.product_id+'" data-product-options=\''+ (p.options ? JSON.stringify(p.options).replace(/\\"/g,'&quot;') : '[]') +'\' data-product-info=\''+(p.product_info?JSON.stringify(p.product_info).replace(/\\"/g,'&quot;'):'{}')+'\'>' +
                '    <div class="relative overflow-hidden">' +
                (p.thumb ? '      <img src="'+p.thumb+'" alt="'+(p.name||'')+'" class="w-full h-64 object-cover object-top" loading="lazy">' : '') +
                (function(){
                  var _s = '';
                  if (p.labels && Array.isArray(p.labels) && p.labels.length) {
                    _s += '      <div class="absolute top-2 left-2 space-y-1">';
                    p.labels.forEach(function(lb){
                      var cls = lb.class || 'bg-primary text-white text-sm px-2 py-1 rounded';
                      var txt = lb.text || '';
                      _s += '        <span class="'+cls+'">'+txt+'</span>';
                    });
                    _s += '      </div>';
                  }
                  return _s;
                })() +
                '    </div>' +
                '    <div class="p-4">' +
                '      <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">'+ (p.name||'') +'</h3>' +
                '      <div class="flex justify-between items-center">' +
                '        <div class="flex flex-col">' +
                '          <span class="text-primary font-bold text-xl">'+ (p.special !== null && p.special !== undefined ? p.special_formatted : p.price_formatted) +'</span>' +
                (p.special !== null && p.special !== undefined ? '          <span class="text-gray-400 line-through text-sm">'+ p.price_formatted +'</span>' : '') +
                '        </div>' +
                '        <button data-product-id="'+p.product_id+'" class="buyButton bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap">Купи</button>' +
                '      </div>' +
                '    </div>' +
                '  </a>' +
                '</div>';
      });
    } else {
      html = '<div class="col-span-3 text-center text-gray-500 py-10">Няма продукти за избраните филтри.</div>';
    }
    grid.innerHTML = html;

    if (pag) pag.innerHTML = json.pagination || '';

    // re-bind cart buttons if needed
    if (window.CartModule && typeof window.CartModule.bindSlideCartEvents === 'function'){
      window.CartModule.bindSlideCartEvents();
    }
  }

  function fetchFiltered(){
    var container = document.getElementById('js-category-filters');
    if (!container) return;
    var params = serializeFilters(container);
    // route (за AJAX)
    params.set('route','product/filter');
    // keep path/category
    var current = new URL(window.location.href);
    if (current.searchParams.get('path')) params.set('path', current.searchParams.get('path'));
    if (current.searchParams.get('category_id')) params.set('category_id', current.searchParams.get('category_id'));

    // Обнови URL на страницата (без route)
    var navParams = new URLSearchParams(params.toString());
    navParams.delete('route');
    history.replaceState(null, '', window.location.pathname + '?' + navParams.toString());

    var url = window.location.origin + '/index.php?' + params.toString();
    fetch(url, { headers: { 'X-Requested-With':'XMLHttpRequest' }})
      .then(function(r){ return r.json(); })
      .then(updateGrid)
      .catch(function(){
        var grid = document.getElementById('js-category-grid');
        var pag  = document.getElementById('js-category-pagination');
        if (grid) grid.innerHTML = '<div class="col-span-3 text-center text-red-600 py-10">Възникна грешка при зареждане на продуктите.</div>';
        if (pag) pag.innerHTML = '';
      });
  }

  function bindEvents(){
    var container = document.getElementById('js-category-filters');
    if (!container) return;

    // Промяна на checkbox-и/селекти
    container.addEventListener('change', function(e){
      var t = e.target; if (!t) return; fetchFiltered();
    });

    // Real-time при въвеждане на цена
    var priceMin = container.querySelector('input[name="price_min"]');
    var priceMax = container.querySelector('input[name="price_max"]');
    if (priceMin) priceMin.addEventListener('input', fetchFiltered);
    if (priceMax) priceMax.addEventListener('input', fetchFiltered);

    var sortSelect = document.getElementById('js-sort-select');
    var orderSelect = document.getElementById('js-order-select');
    var limitSelect = document.getElementById('js-limit-select');

    function syncAndFetch(sel, key){ if (!sel) return; sel.addEventListener('change', function(){
      var params = new URLSearchParams(window.location.search);
      params.set(key, sel.value); params.delete('page');
      history.replaceState(null, '', window.location.pathname + '?' + params.toString());
      fetchFiltered();
    }); }

    syncAndFetch(sortSelect, 'sort');
    syncAndFetch(orderSelect, 'order');
    syncAndFetch(limitSelect, 'limit');

    // Delegate clicks on pagination links
    var pag = document.getElementById('js-category-pagination');
    if (pag) pag.addEventListener('click', function(e){
      var a = e.target.closest('a'); if (!a) return;
      var pageMatch = a.getAttribute('href');
      if (pageMatch){
        e.preventDefault();
        var url = new URL(pageMatch, window.location.origin);
        var p = url.searchParams.get('page') || '1';
        var params = new URLSearchParams(window.location.search);
        params.set('page', p);
        history.replaceState(null, '', window.location.pathname + '?' + params.toString());
        fetchFiltered();
      }
    });
  }
  // Custom dropdowns for selects (sort/order/limit)
  function initDropdowns(){
    if (ddInitialized) return; ddInitialized = true;
    var selects = [
      document.getElementById('js-sort-select'),
      document.getElementById('js-order-select'),
      document.getElementById('js-limit-select')
    ];
    selects.forEach(function(sel){ if (sel) enhanceSelect(sel); });

    // Close on outside click
    document.addEventListener('click', function(e){
      if (!e.target.closest('.js-dd')) closeAllDropdowns();
    });
    // Close on ESC
    document.addEventListener('keydown', function(e){ if (e.key === 'Escape') closeAllDropdowns(); });
  }

  function enhanceSelect(select){
    if (select.dataset.enhanced === '1') return; select.dataset.enhanced = '1';

    // Wrapper
    var wrapper = document.createElement('div');
    wrapper.className = 'js-dd relative inline-block w-48';
    wrapper.setAttribute('data-for', select.id || '');

    // Button
    var button = document.createElement('button');
    button.type = 'button';
    button.className = 'w-full bg-white border border-gray-300 rounded px-3 py-2 flex justify-between items-center hover:border-primary focus:ring-2 focus:ring-primary';
    button.setAttribute('aria-haspopup', 'listbox');
    button.setAttribute('aria-expanded', 'false');

    // Label text
    var labelSpan = document.createElement('span');
    labelSpan.className = 'truncate';
    labelSpan.textContent = select.options[select.selectedIndex] ? select.options[select.selectedIndex].text : '';

    var caret = document.createElement('i');
    caret.className = 'ri-arrow-down-s-line';

    button.appendChild(labelSpan); button.appendChild(caret);

    // List
    var list = document.createElement('ul');
    list.className = 'absolute mt-1 left-0 right-0 bg-white border border-gray-200 rounded shadow z-20 max-h-60 overflow-auto hidden';
    list.setAttribute('role','listbox');

    for (var i=0;i<select.options.length;i++){
      var opt = select.options[i];
      var li = document.createElement('li');
      li.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer';
      li.setAttribute('role','option');
      li.setAttribute('data-value', opt.value);
      li.textContent = opt.text;
      if (opt.selected) { li.classList.add('bg-gray-50'); li.setAttribute('aria-selected','true'); }
      li.addEventListener('click', function(ev){
        var v = ev.currentTarget.getAttribute('data-value');
        select.value = v;
        // Update label
        labelSpan.textContent = ev.currentTarget.textContent;
        // Mark selected
        Array.prototype.forEach.call(list.children, function(x){ x.classList.remove('bg-gray-50'); x.removeAttribute('aria-selected'); });
        ev.currentTarget.classList.add('bg-gray-50'); ev.currentTarget.setAttribute('aria-selected','true');
        // Trigger native change (our listeners handle URL + fetch)
        var evt = new Event('change', {bubbles:true}); select.dispatchEvent(evt);
        // Close
        list.classList.add('hidden'); button.setAttribute('aria-expanded','false');
      });
      list.appendChild(li);
    }

    // Keyboard support
    var activeIndex = select.selectedIndex || 0;
    button.addEventListener('keydown', function(e){
      if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
        e.preventDefault();
        list.classList.remove('hidden'); button.setAttribute('aria-expanded','true');
        var items = list.querySelectorAll('li');
        if (!items.length) return;
        if (e.key === 'ArrowDown') activeIndex = Math.min(activeIndex+1, items.length-1);
        else activeIndex = Math.max(activeIndex-1, 0);
        items[activeIndex].focus ? items[activeIndex].focus() : null;
        list.scrollTop = items[activeIndex].offsetTop - 40;
      } else if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        button.click();
      }
    });

    button.addEventListener('click', function(){
      var isOpen = !list.classList.contains('hidden');
      closeAllDropdowns();
      if (!isOpen){ list.classList.remove('hidden'); button.setAttribute('aria-expanded','true'); }
    });

    // Place wrapper before select and move select inside (hidden)
    select.style.position = 'absolute'; select.style.left = '-9999px'; select.tabIndex = -1;
    var parent = select.parentNode; parent.insertBefore(wrapper, select);
    wrapper.appendChild(button); wrapper.appendChild(list); wrapper.appendChild(select);

    // Sync label on native change (e.g., applyUrlToFilters)
    select.addEventListener('change', function(){
      var txt = select.options[select.selectedIndex] ? select.options[select.selectedIndex].text : '';
      labelSpan.textContent = txt;
      Array.prototype.forEach.call(list.children, function(x){
        if (x.getAttribute('data-value') === select.value){ x.classList.add('bg-gray-50'); x.setAttribute('aria-selected','true'); }
        else { x.classList.remove('bg-gray-50'); x.removeAttribute('aria-selected'); }
      });
    });
  }

  function closeAllDropdowns(){
    document.querySelectorAll('.js-dd ul[role="listbox"]').forEach(function(ul){ ul.classList.add('hidden'); });
    document.querySelectorAll('.js-dd button[aria-haspopup="listbox"]').forEach(function(btn){ btn.setAttribute('aria-expanded','false'); });
  }


  function applyUrlToFilters(){
    var params = new URLSearchParams(window.location.search);
    var container = document.getElementById('js-category-filters'); if (!container) return;
    var v;
    v = params.get('price_min'); if (v !== null) { var el = container.querySelector('input[name="price_min"]'); if (el) el.value = v; }
    v = params.get('price_max'); if (v !== null) { var el2 = container.querySelector('input[name="price_max"]'); if (el2) el2.value = v; }
    v = params.get('in_stock'); var ch = container.querySelector('input[name="in_stock"]'); if (ch) ch.checked = (v === '1');
    v = params.get('manufacturer_id'); if (v) { var ids = v.split(','); ids.forEach(function(id){ var el = container.querySelector('input[name="manufacturer_id[]"][value="' + id + '"]'); if (el) el.checked = true; }); }
    // селекти
    var s = document.getElementById('js-sort-select'); if (s && params.get('sort')) s.value = params.get('sort');


    var o = document.getElementById('js-order-select'); if (o && params.get('order')) o.value = params.get('order');
    var l = document.getElementById('js-limit-select'); if (l && params.get('limit')) l.value = params.get('limit');
  }

  if (document.readyState === 'loading'){
    document.addEventListener('DOMContentLoaded', function(){ if (window.ProductCategoryModule) window.ProductCategoryModule.init(); });
  } else {
    if (window.ProductCategoryModule) window.ProductCategoryModule.init();
  }
  // ProductCategoryModule (extends FrontendModule)
  const ProductCategoryModule = Object.create(window.FrontendModule || {});
  Object.assign(ProductCategoryModule, {
    init: function(){ applyUrlToFilters(); bindEvents(); initDropdowns(); },
    fetchFiltered: fetchFiltered,
    updateGrid: updateGrid,
    serializeFilters: serializeFilters,
    bindEvents: bindEvents
  });
  window.ProductCategoryModule = ProductCategoryModule;

})();

