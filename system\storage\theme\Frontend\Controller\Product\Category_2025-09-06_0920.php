<?php

namespace Theme25\Frontend\Controller\Product;

/**
 * Контролер за листинг на продукти по категория (route: product/category)
 */
class Category extends Cards
{
    public function __construct($registry)
    {
        parent::__construct($registry, 'product/category');
    }

    public function index()
    {
        $this->addFrontendScriptWithVersion('product-category.js', 'footer');

        // 1) Валидация и извличане на category_id от URL (path)
        $category_id = $this->getCategoryIdFromRequest();
        if ($category_id <= 0) {
            return $this->renderNotFound();
        }

        // 2) Зареждане на нужните модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'catalog/product'  => 'productModel',
            'tool/image'       => 'imageModel'
        ]);

        // 3) Проверка дали категорията съществува и е активна
        $category_info = $this->categoryModel->getCategory($category_id);
        if (!$category_info || (isset($category_info['status']) && (int)$category_info['status'] !== 1)) {
            return $this->renderNotFound();
        }

        // 4) SEO заглавия и мета
        $title = $category_info['meta_title'] ?? $category_info['name'] ?? 'Категория';
        $this->document->setTitle($title);
        if (!empty($category_info['meta_description'])) {
            $this->document->setDescription($category_info['meta_description']);
        }
        if (!empty($category_info['meta_keyword'])) {
            $this->document->setKeywords($category_info['meta_keyword']);
        }

        // 5) Breadcrumbs от path
        $breadcrumbs = $this->buildBreadcrumbs($category_id);

        // 6) Сортиране, лимит и страница
        list($sort, $order) = $this->getSortAndOrder();
        $limit = $this->getLimit(); // 10/20/50/100 (по подразбиране 20)
        $page  = $this->requestGet('page') ? max(1, (int)$this->requestGet('page')) : 1;
        $start = ($page - 1) * $limit;

        // 7) Филтър данни (минимален сет; UI пази всички параметри в URL)
        $filter_data = [
            'filter_category_id' => $category_id,
            'filter_sub_category' => true,
            'sort'  => $sort,
            'order' => $order,
            'start' => $start,
            'limit' => $limit,
        ];
        // TODO (следваща стъпка): разширяване с производител, цена, рейтинг и др. чрез специализиран модел

        // 8) Данни за продукти и общ брой
        $results       = $this->productModel->getProducts($filter_data);
        $total_results = $this->productModel->getTotalProducts($filter_data);

        // 9) Подготовка на карти
        $products = [];
        if (!empty($results)) {
            foreach ($results as $row) {
                // Зареждаме пълния продукт за картата (същия подход като в Search.php)
                $productInfo = $this->productModel->getProduct((int)$row['product_id']);
                if (!$productInfo) continue;
                $products[] = $this->prepareProductCardData($productInfo, false);
            }
        }

        // 10) Пагинация с \\Theme25\\Pagination и запазване на всички query параметри
        $pagination_html = $this->buildPagination($total_results, $page, $limit);

        // 10.1) Филтърни опции за UI
        $this->loadModelAs('catalog/product/filter', 'productFilterModel');
        $filterOptions = $this->productFilterModel->getFilterOptions($category_id);

        // 10.2) Активни филтри (за бутон "Изчисти филтрите")
        $g = isset($_GET) && is_array($_GET) ? $_GET : [];
        $has_active_filters = false;
        foreach (['price_min','price_max','manufacturer_id'] as $k) {
            if (isset($g[$k]) && $g[$k] !== '' && $g[$k] !== null) { $has_active_filters = true; break; }
        }
        if (!$has_active_filters && isset($g['in_stock']) && (string)$g['in_stock'] === '1') { $has_active_filters = true; }

        // 11) Данни към шаблона
        $this->data['category_title']       = $category_info['name'] ?? '';
        $this->data['category_description'] = $category_info['description'] ? html_entity_decode($category_info['description']) : '';
        $this->data['breadcrumbs']          = $breadcrumbs;
        $this->data['products']             = $products;
        $this->data['total_results']        = (int)$total_results;
        $this->data['pagination']           = $pagination_html;
        $this->data['current_page']         = $page;
        $this->data['current_limit']        = $limit;
        $this->data['limits']               = $this->getLimitOptions();
        $this->data['sorts']                = $this->getSortOptions($sort, $order);
        $this->data['current_sort']         = $sort;
        $this->data['current_order']        = $order;

        // 11.1) Филтър опции към UI
        $this->data['filters']               = $filterOptions;
        $this->data['manufacturers']         = isset($filterOptions['manufacturers']) ? $filterOptions['manufacturers'] : [];
        $this->data['price_min_available']   = isset($filterOptions['price']['min']) ? (float)$filterOptions['price']['min'] : 0.0;
        $this->data['price_max_available']   = isset($filterOptions['price']['max']) ? (float)$filterOptions['price']['max'] : 0.0;

        // 12) Structured Data (JSON-LD) – basic ProductList
        $this->data['json_ld'] = $this->buildJsonLd($products, $category_info);

        // URL помощници за шаблона
        $this->data['clear_filters_url'] = $this->buildUrlWithOverrides(['page' => null, 'limit' => null, 'sort' => null, 'order' => null]);
        // За JS селектите подаваме само стойности, не URL-и
        $this->data['order_urls'] = [ 'ASC' => 'ASC', 'DESC' => 'DESC' ];
        $this->data['limit_urls'] = [ 10 => 10, 20 => 20, 50 => 50, 100 => 100 ];
        // Флаг за активни филтри + текущи стойности
        $this->data['has_active_filters'] = $has_active_filters;
        $this->data['price_min'] = isset($g['price_min']) ? (string)$g['price_min'] : '';
        $this->data['price_max'] = isset($g['price_max']) ? (string)$g['price_max'] : '';
        $this->data['in_stock']  = isset($g['in_stock']) && (string)$g['in_stock'] === '1' ? 1 : 0;

        return $this->renderTemplateWithDataAndOutput('product/category');
    }

    private function buildUrlWithOverrides(array $overrides): string
    {
        // Базов линк + path към текущата категория, ако е наличен
        $params = [];
        foreach ((array)$_GET as $k => $v) {
            if ($v === '' || $v === null) continue;
            $params[$k] = (string)$v;
        }
        foreach ($overrides as $k => $v) {
            if ($v === null) {
                unset($params[$k]);
            } else {
                $params[$k] = (string)$v;
            }
        }
        $query = http_build_query($params);
        $base  = $this->getLink('product/category');
        return $query ? ($base . '&' . $query) : $base;
    }

    private function buildSortUrls(): array
    {
        $urls = [];
        foreach (['default','name','price','date_added','popularity','rating'] as $key) {
            $urls[$key] = $this->buildUrlWithOverrides(['sort' => $key, 'page' => null]);
        }
        return $urls;
    }

    private function buildLimitUrls(): array
    {
        $urls = [];
        foreach ([10,20,50,100] as $val) {
            $urls[$val] = $this->buildUrlWithOverrides(['limit' => (string)$val, 'page' => null]);
        }
        return $urls;
    }

    private function renderNotFound()
    {
        // Може да се използва централен 404 контролер при нужда
        $this->document->setTitle('Категория не е намерена');
        $this->setData([
            'category_title' => 'Категория не е намерена',
            'category_description' => ''
        ]);
        return $this->renderTemplateWithDataAndOutput('error/under_construction');
    }

    private function getCategoryIdFromRequest(): int
    {
        // OpenCart обичайно подава path=10_20_30 (последната е текущата)
        $path = (string)($this->requestGet('path') ?: '');
        if ($path === '') return (int)$this->requestGet('category_id'); // fallback
        $parts = explode('_', $path);
        $last  = end($parts);
        return ctype_digit((string)$last) ? (int)$last : 0;
    }

    private function buildBreadcrumbs(int $category_id): array
    {
        $breadcrumbs = [];
        // Начало
        $breadcrumbs[] = [
            'text' => 'Начало',
            'href' => $this->getLink('common/home')
        ];

        // Ако имаме path, запазваме целия и генерираме линкове
        $path = (string)($this->requestGet('path') ?: '');
        if ($path !== '') {
            $accum = [];
            foreach (explode('_', $path) as $cid) {
                if (!ctype_digit((string)$cid)) continue;
                $accum[] = $cid;
                $info = $this->categoryModel->getCategory((int)$cid);
                if (!$info) continue;
                $breadcrumbs[] = [
                    'text' => $info['name'] ?? ('Категория ' . (int)$cid),
                    'href' => $this->getLink('product/category', 'path=' . implode('_', $accum))
                ];
            }
        } else {
            // Без path – поне текущата категория
            $info = $this->categoryModel->getCategory($category_id);
            if ($info) {
                $breadcrumbs[] = [
                    'text' => $info['name'] ?? 'Категория',
                    'href' => $this->getLink('product/category', 'category_id=' . $category_id)
                ];
            }
        }

        return $breadcrumbs;
    }

    private function getSortAndOrder(): array
    {
        $allowedSorts = [
            'default'      => 'p.sort_order',
            'name'         => 'pd.name',
            'price'        => 'p.price',
            'date_added'   => 'p.date_added',
            'rating'       => 'rating',
            'popularity'   => 'p.viewed',
        ];

        $sortKey = $this->requestGet('sort') ?: 'default';
        $order   = strtoupper($this->requestGet('order') ?: 'ASC');
        if (!isset($allowedSorts[$sortKey])) {
            $sortKey = 'default';
        }
        if (!in_array($order, ['ASC', 'DESC'], true)) {
            $order = 'ASC';
        }
        return [$allowedSorts[$sortKey], $order];
    }

    private function getLimit(): int
    {
        $limit = (int)($this->requestGet('limit') ?: 12);
        if (!in_array($limit, [12, 24, 36], true)) {
            $limit = 12;
        }
        return $limit;
    }

    private function getLimitOptions(): array
    {
        return [
            ['value' => 12,  'text' => '12 на страница'],
            ['value' => 24,  'text' => '24 на страница'],
            ['value' => 36,  'text' => '36 на страница'],
        ];
    }

    private function getSortOptions(string $currentSort, string $currentOrder): array
    {
        // Връщаме конфигурации за UI
        return [
            ['key' => 'default',    'text' => 'По подразбиране'],
            ['key' => 'name',       'text' => 'Име'],
            ['key' => 'price',      'text' => 'Цена'],
            ['key' => 'date_added', 'text' => 'Най-нови'],
            ['key' => 'popularity', 'text' => 'Популярност'],
            ['key' => 'rating',     'text' => 'Рейтинг'],
        ];
    }

    private function buildPagination(int $total, int $page, int $limit): string
    {
        if ($total <= $limit) return '';

        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page  = $page;
        $pagination->limit = $limit;

        // Запазваме всички текущи query параметри (без page)
        $base = $this->getLink('product/category');
        $qs   = $this->getPreservedQueryString(['page']);
        $url  = $base . ($qs ? '&' . $qs : '');

        $pagination->url = $url . '&page={page}';
        $pagination->setLimits($this->getLimitOptions());
        $pagination->setLimitUrl($url . '&limit={limit}');
        $pagination->setProductText('продукта');

        return $pagination->render();
    }

    private function getPreservedQueryString(array $exclude = []): string
    {
        $params = [];
        if (!empty($_GET)) {
            foreach ($_GET as $k => $v) {
                if (in_array($k, $exclude, true)) continue;
                if ($v === '' || $v === null) continue;
                $params[] = rawurlencode($k) . '=' . rawurlencode((string)$v);
            }
        }
        return implode('&', $params);
    }

    private function buildJsonLd(array $products, array $category_info): string
    {
        $items = [];
        foreach ($products as $p) {
            $items[] = [
                '@type' => 'Product',
                'name'  => $p['name'] ?? '',
                'url'   => $p['href'] ?? '',
                'image' => $p['thumb'] ?? '',
                'offers' => [
                    '@type' => 'Offer',
                    'priceCurrency' => $this->getConfig('config_currency'),
                    'price' => isset($p['special']) && $p['special'] !== null ? (float)$p['special'] : (float)$p['price'],
                    'availability' => 'http://schema.org/InStock'
                ]
            ];
        }

        $data = [
            '@context' => 'http://schema.org',
            '@type'    => 'CollectionPage',
            'name'     => $category_info['name'] ?? 'Категория',
            'hasPart'  => $items,
        ];

        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}

