(function(){
  'use strict';

  function serializeFilters(container){
    // Започваме от текущите параметри, но ще синхронизираме с UI стойностите
    var params = new URLSearchParams(window.location.search);
    var priceMin = container.querySelector('input[name="price_min"]');
    var priceMax = container.querySelector('input[name="price_max"]');
    var inStock  = container.querySelector('input[name="in_stock"]');
    var manufEls = container.querySelectorAll('input[name="manufacturer_id[]"]');
    var sortSel  = document.getElementById('js-sort-select');
    var orderSel = document.getElementById('js-order-select');
    var limitSel = document.getElementById('js-limit-select');

    if (priceMin && priceMin.value) params.set('price_min', priceMin.value); else params.delete('price_min');
    if (priceMax && priceMax.value) params.set('price_max', priceMax.value); else params.delete('price_max');
    if (inStock && inStock.checked) params.set('in_stock', '1'); else params.delete('in_stock');

    var manuf = [];
    manufEls.forEach(function(el){ if (el.checked) manuf.push(el.value); });
    if (manuf.length) params.set('manufacturer_id', manuf.join(',')); else params.delete('manufacturer_id');

    if (sortSel && sortSel.value) params.set('sort', sortSel.value); else params.delete('sort');
    if (orderSel && orderSel.value) params.set('order', orderSel.value); else params.delete('order');
    if (limitSel && limitSel.value) params.set('limit', limitSel.value); else params.delete('limit');

    // reset page когато филтрите се сменят
    params.delete('page');
    return params;
  }

  function updateGrid(json){
    var grid = document.getElementById('js-category-grid');
    var pag  = document.getElementById('js-category-pagination');
    if (!grid) return;

    if (!json || !json.success){ return; }

    // Build cards
    var html = '';
    if (json.products && json.products.length){
      json.products.forEach(function(p){
        html += '\n<div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:scale-105">' +
                '  <a href="'+ (p.href||'#') +'" class="block group" data-product-id="'+p.product_id+'" data-product-options=\''+ (p.options ? JSON.stringify(p.options).replace(/\\"/g,'&quot;') : '[]') +'\' data-product-info=\''+(p.product_info?JSON.stringify(p.product_info).replace(/\\"/g,'&quot;'):'{}')+'\'>' +
                '    <div class="relative overflow-hidden">' +
                (p.thumb ? '      <img src="'+p.thumb+'" alt="'+(p.name||'')+'" class="w-full h-64 object-cover object-top" loading="lazy">' : '') +
                (p.labels && p.labels.special ? '      <div class="absolute top-2 left-2 bg-primary text-white text-sm px-2 py-1 rounded">-'+(p.labels.special.percent||'')+'%</div>' : '') +
                '      ' + (p.labels && p.labels.new && !(p.labels && p.labels.special) ? '<div class="absolute top-2 left-2 bg-green-600 text-white text-sm px-2 py-1 rounded">Ново</div>' : '') +
                '      ' + (p.labels && p.labels.new && (p.labels && p.labels.special) ? '<div class="absolute top-10 left-2 bg-green-600 text-white text-sm px-2 py-1 rounded">Ново</div>' : '') +
                '      ' + (p.labels && p.labels.out_of_stock ? '<div class="absolute top-2 right-2 bg-red-600 text-white text-sm px-2 py-1 rounded">Изчерпан</div>' : '') +

                '    </div>' +
                '    <div class="p-4">' +
                '      <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">'+ (p.name||'') +'</h3>' +
                '      <div class="flex justify-between items-center">' +
                '        <div class="flex flex-col">' +
                '          <span class="text-primary font-bold text-xl">'+ (p.special !== null && p.special !== undefined ? p.special_formatted : p.price_formatted) +'</span>' +
                (p.special !== null && p.special !== undefined ? '          <span class="text-gray-400 line-through text-sm">'+ p.price_formatted +'</span>' : '') +
                '        </div>' +
                '        <button data-product-id="'+p.product_id+'" class="buyButton bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap">Купи</button>' +
                '      </div>' +
                '    </div>' +
                '  </a>' +
                '</div>';
      });
    } else {
      html = '<div class="col-span-3 text-center text-gray-500 py-10">Няма продукти за избраните филтри.</div>';
    }
    grid.innerHTML = html;

    if (pag) pag.innerHTML = json.pagination || '';

    // re-bind cart buttons if needed
    if (window.CartModule && typeof window.CartModule.bindSlideCartEvents === 'function'){
      window.CartModule.bindSlideCartEvents();
    }
  }

  function fetchFiltered(){
    var container = document.getElementById('js-category-filters');
    if (!container) return;
    var params = serializeFilters(container);
    // route (за AJAX)
    params.set('route','product/filter');
    // keep path/category
    var current = new URL(window.location.href);
    if (current.searchParams.get('path')) params.set('path', current.searchParams.get('path'));
    if (current.searchParams.get('category_id')) params.set('category_id', current.searchParams.get('category_id'));

    // Обнови URL на страницата (без route)
    var navParams = new URLSearchParams(params.toString());
    navParams.delete('route');
    history.replaceState(null, '', window.location.pathname + '?' + navParams.toString());

    var url = window.location.origin + '/index.php?' + params.toString();
    fetch(url, { headers: { 'X-Requested-With':'XMLHttpRequest' }})
      .then(function(r){ return r.json(); })
      .then(updateGrid)
      .catch(function(){
        var grid = document.getElementById('js-category-grid');
        var pag  = document.getElementById('js-category-pagination');
        if (grid) grid.innerHTML = '<div class="col-span-3 text-center text-red-600 py-10">Възникна грешка при зареждане на продуктите.</div>';
        if (pag) pag.innerHTML = '';
      });
  }

  function bindEvents(){
    var container = document.getElementById('js-category-filters');
    if (!container) return;

    // Промяна на checkbox-и/селекти
    container.addEventListener('change', function(e){
      var t = e.target; if (!t) return; fetchFiltered();
    });

    // Real-time при въвеждане на цена
    var priceMin = container.querySelector('input[name="price_min"]');
    var priceMax = container.querySelector('input[name="price_max"]');
    if (priceMin) priceMin.addEventListener('input', fetchFiltered);
    if (priceMax) priceMax.addEventListener('input', fetchFiltered);

    var sortSelect = document.getElementById('js-sort-select');
    var orderSelect = document.getElementById('js-order-select');
    var limitSelect = document.getElementById('js-limit-select');

    function syncAndFetch(sel, key){ if (!sel) return; sel.addEventListener('change', function(){
      var params = new URLSearchParams(window.location.search);
      params.set(key, sel.value); params.delete('page');
      history.replaceState(null, '', window.location.pathname + '?' + params.toString());
      fetchFiltered();
    }); }

    syncAndFetch(sortSelect, 'sort');
    syncAndFetch(orderSelect, 'order');
    syncAndFetch(limitSelect, 'limit');

    // Delegate clicks on pagination links
    var pag = document.getElementById('js-category-pagination');
    if (pag) pag.addEventListener('click', function(e){
      var a = e.target.closest('a'); if (!a) return;
      var pageMatch = a.getAttribute('href');
      if (pageMatch){
        e.preventDefault();
        var url = new URL(pageMatch, window.location.origin);
        var p = url.searchParams.get('page') || '1';
        var params = new URLSearchParams(window.location.search);
        params.set('page', p);
        history.replaceState(null, '', window.location.pathname + '?' + params.toString());
        fetchFiltered();
      }
    });
  }

  function applyUrlToFilters(){
    var params = new URLSearchParams(window.location.search);
    var container = document.getElementById('js-category-filters'); if (!container) return;
    var v;
    v = params.get('price_min'); if (v !== null) { var el = container.querySelector('input[name="price_min"]'); if (el) el.value = v; }
    v = params.get('price_max'); if (v !== null) { var el2 = container.querySelector('input[name="price_max"]'); if (el2) el2.value = v; }
    v = params.get('in_stock'); var ch = container.querySelector('input[name="in_stock"]'); if (ch) ch.checked = (v === '1');
    v = params.get('manufacturer_id'); if (v) { var ids = v.split(','); ids.forEach(function(id){ var el = container.querySelector('input[name="manufacturer_id[]"][value="' + id + '"]'); if (el) el.checked = true; }); }
    // селекти
    var s = document.getElementById('js-sort-select'); if (s && params.get('sort')) s.value = params.get('sort');


    var o = document.getElementById('js-order-select'); if (o && params.get('order')) o.value = params.get('order');
    var l = document.getElementById('js-limit-select'); if (l && params.get('limit')) l.value = params.get('limit');
  }

  if (document.readyState === 'loading'){
    document.addEventListener('DOMContentLoaded', function(){ if (window.ProductCategoryModule) window.ProductCategoryModule.init(); });
  } else {
    if (window.ProductCategoryModule) window.ProductCategoryModule.init();
  }
  // ProductCategoryModule (extends FrontendModule)
  window.ProductCategoryModule = Object.assign(Object.create(window.FrontendModule || {}), {
    init: function(){ applyUrlToFilters(); bindEvents(); },
    fetchFiltered: fetchFiltered,
    updateGrid: updateGrid,
    serializeFilters: serializeFilters,
    bindEvents: bindEvents
  });

})();

