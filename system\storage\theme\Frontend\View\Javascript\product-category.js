/**
 * Product Category Module
 * Extends FrontendModule for category page functionality
 */
(function() {
    'use strict';

    // Create ProductCategoryModule by extending FrontendModule
    const ProductCategoryModule = Object.create(window.FrontendModule || {});

    // Override/extend configuration
    Object.assign(ProductCategoryModule, {
        // Module-specific configuration
        moduleConfig: {
            ddInitialized: false, // Флаг за еднократна инициализация на custom dropdown-и
            baseUrl: window.location.origin
        },

        /**
         * Initialize the module
         */
        init: function() {
            this.applyUrlToFilters();
            this.bindEvents();
            this.initDropdowns();
            this.initPriceSlider();
        },

        /**
         * Serialize filters from UI elements
         */
        serializeFilters: function(container) {
            // Започваме от текущите параметри, но ще синхронизираме с UI стойностите
            var params = new URLSearchParams(window.location.search);
            var priceMin = container.querySelector('input[name="price_min"]');
            var priceMax = container.querySelector('input[name="price_max"]');
            var inStock  = container.querySelector('input[name="in_stock"]');
            var manufEls = container.querySelectorAll('input[name="manufacturer_id[]"]');
            var sortSel  = document.getElementById('js-sort-select');
            var orderSel = document.getElementById('js-order-select');
            var limitSel = document.getElementById('js-limit-select');

            if (priceMin && priceMin.value) params.set('price_min', priceMin.value); else params.delete('price_min');
            if (priceMax && priceMax.value) params.set('price_max', priceMax.value); else params.delete('price_max');
            if (inStock && inStock.checked) params.set('in_stock', '1'); else params.delete('in_stock');

            var manuf = [];
            manufEls.forEach(function(el){ if (el.checked) manuf.push(el.value); });
            if (manuf.length) params.set('manufacturer_id', manuf.join(',')); else params.delete('manufacturer_id');

            if (sortSel && sortSel.value) params.set('sort', sortSel.value); else params.delete('sort');
            if (orderSel && orderSel.value) params.set('order', orderSel.value); else params.delete('order');
            if (limitSel && limitSel.value) params.set('limit', limitSel.value); else params.delete('limit');

            // reset page когато филтрите се сменят
            params.delete('page');
            return params;
        },



        /**
         * Navigate to filtered page via HTTP redirect
         */
        navigateToFiltered: function() {
            var container = document.querySelector('.bg-white.rounded-lg.shadow-md.p-6.sticky');
            if (!container) return;
            var params = this.serializeFilters(container);

            // Генерираме новия URL за пренасочване
            var newUrl = window.location.pathname + '?' + params.toString();
            window.location.href = newUrl;
        },

        /**
         * Bind event listeners
         */
        bindEvents: function() {
            var container = document.querySelector('.bg-white.rounded-lg.shadow-md.p-6.sticky');
            if (!container) return;

            // Промяна на checkbox-и/селекти - пренасочване към нова страница
            container.addEventListener('change', function(e){
                var t = e.target; if (!t) return; this.navigateToFiltered();
            }.bind(this));

            // При въвеждане на цена - пренасочване с малко забавяне
            var priceMin = container.querySelector('input[name="price_min"]');
            var priceMax = container.querySelector('input[name="price_max"]');
            var priceTimeout;

            var handlePriceChange = function() {
                clearTimeout(priceTimeout);
                priceTimeout = setTimeout(this.navigateToFiltered.bind(this), 500);
            }.bind(this);

            if (priceMin) priceMin.addEventListener('input', handlePriceChange);
            if (priceMax) priceMax.addEventListener('input', handlePriceChange);

            var sortSelect = document.getElementById('js-sort-select');
            var orderSelect = document.getElementById('js-order-select');
            var limitSelect = document.getElementById('js-limit-select');

            var syncAndNavigate = function(sel, key){
                if (!sel) return;
                sel.addEventListener('change', function(){
                    var params = new URLSearchParams(window.location.search);
                    params.set(key, sel.value); params.delete('page');
                    var newUrl = window.location.pathname + '?' + params.toString();
                    window.location.href = newUrl;
                }.bind(this));
            }.bind(this);

            syncAndNavigate(sortSelect, 'sort');
            syncAndNavigate(orderSelect, 'order');
            syncAndNavigate(limitSelect, 'limit');

            // Pagination links използват стандартно HTML поведение - няма нужда от JavaScript
        },
        /**
         * Initialize custom dropdowns for selects (sort/order/limit)
         */
        initDropdowns: function() {
            if (this.moduleConfig.ddInitialized) return;
            this.moduleConfig.ddInitialized = true;

            var selects = [
                document.getElementById('js-sort-select'),
                document.getElementById('js-order-select'),
                document.getElementById('js-limit-select')
            ];
            selects.forEach(function(sel){ if (sel) this.enhanceSelect(sel); }.bind(this));

            // Close on outside click
            document.addEventListener('click', function(e){
                if (!e.target.closest('.js-dd')) this.closeAllDropdowns();
            }.bind(this));
            // Close on ESC
            document.addEventListener('keydown', function(e){
                if (e.key === 'Escape') this.closeAllDropdowns();
            }.bind(this));
        },

        /**
         * Enhance select element with custom dropdown
         */
        enhanceSelect: function(select) {
            if (select.dataset.enhanced === '1') return;
            select.dataset.enhanced = '1';

            // Wrapper
            var wrapper = document.createElement('div');
            wrapper.className = 'js-dd relative inline-block w-48';
            wrapper.setAttribute('data-for', select.id || '');

            // Button
            var button = document.createElement('button');
            button.type = 'button';
            button.className = 'w-full bg-white border border-gray-300 rounded px-3 py-2 flex justify-between items-center hover:border-primary focus:ring-2 focus:ring-primary';
            button.setAttribute('aria-haspopup', 'listbox');
            button.setAttribute('aria-expanded', 'false');

            // Label text
            var labelSpan = document.createElement('span');
            labelSpan.className = 'truncate';
            labelSpan.textContent = select.options[select.selectedIndex] ? select.options[select.selectedIndex].text : '';

            var caret = document.createElement('i');
            caret.className = 'ri-arrow-down-s-line';

            button.appendChild(labelSpan);
            button.appendChild(caret);

            // List
            var list = document.createElement('ul');
            list.className = 'absolute mt-1 left-0 right-0 bg-white border border-gray-200 rounded shadow z-20 max-h-60 overflow-auto hidden';
            list.setAttribute('role','listbox');

            for (var i=0;i<select.options.length;i++){
                var opt = select.options[i];
                var li = document.createElement('li');
                li.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer';
                li.setAttribute('role','option');
                li.setAttribute('data-value', opt.value);
                li.textContent = opt.text;
                if (opt.selected) { li.classList.add('bg-gray-50'); li.setAttribute('aria-selected','true'); }
                li.addEventListener('click', function(ev){
                    var v = ev.currentTarget.getAttribute('data-value');
                    select.value = v;
                    // Update label
                    labelSpan.textContent = ev.currentTarget.textContent;
                    // Mark selected
                    Array.prototype.forEach.call(list.children, function(x){ x.classList.remove('bg-gray-50'); x.removeAttribute('aria-selected'); });
                    ev.currentTarget.classList.add('bg-gray-50'); ev.currentTarget.setAttribute('aria-selected','true');
                    // Trigger native change (our listeners handle URL + fetch)
                    var evt = new Event('change', {bubbles:true}); select.dispatchEvent(evt);
                    // Close
                    list.classList.add('hidden'); button.setAttribute('aria-expanded','false');
                });
                list.appendChild(li);
            }

            // Keyboard support
            var activeIndex = select.selectedIndex || 0;
            button.addEventListener('keydown', function(e){
                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    list.classList.remove('hidden'); button.setAttribute('aria-expanded','true');
                    var items = list.querySelectorAll('li');
                    if (!items.length) return;
                    if (e.key === 'ArrowDown') activeIndex = Math.min(activeIndex+1, items.length-1);
                    else activeIndex = Math.max(activeIndex-1, 0);
                    items[activeIndex].focus ? items[activeIndex].focus() : null;
                    list.scrollTop = items[activeIndex].offsetTop - 40;
                } else if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    button.click();
                }
            });

            button.addEventListener('click', function(){
                var isOpen = !list.classList.contains('hidden');
                this.closeAllDropdowns();
                if (!isOpen){ list.classList.remove('hidden'); button.setAttribute('aria-expanded','true'); }
            }.bind(this));

            // Place wrapper before select and move select inside (hidden)
            select.style.position = 'absolute'; select.style.left = '-9999px'; select.tabIndex = -1;
            var parent = select.parentNode; parent.insertBefore(wrapper, select);
            wrapper.appendChild(button); wrapper.appendChild(list); wrapper.appendChild(select);

            // Sync label on native change (e.g., applyUrlToFilters)
            select.addEventListener('change', function(){
                var txt = select.options[select.selectedIndex] ? select.options[select.selectedIndex].text : '';
                labelSpan.textContent = txt;
                Array.prototype.forEach.call(list.children, function(x){
                    if (x.getAttribute('data-value') === select.value){ x.classList.add('bg-gray-50'); x.setAttribute('aria-selected','true'); }
                    else { x.classList.remove('bg-gray-50'); x.removeAttribute('aria-selected'); }
                });
            });
        },

        /**
         * Close all dropdown menus
         */
        closeAllDropdowns: function() {
            document.querySelectorAll('.js-dd ul[role="listbox"]').forEach(function(ul){ ul.classList.add('hidden'); });
            document.querySelectorAll('.js-dd button[aria-haspopup="listbox"]').forEach(function(btn){ btn.setAttribute('aria-expanded','false'); });
        },

        /**
         * Apply URL parameters to filter UI elements
         */
        applyUrlToFilters: function() {
            var params = new URLSearchParams(window.location.search);
            var container = document.getElementById('js-category-filters');
            if (!container) return;

            var v;
            v = params.get('price_min');
            if (v !== null) {
                var el = container.querySelector('input[name="price_min"]');
                if (el) el.value = v;
            }
            v = params.get('price_max');
            if (v !== null) {
                var el2 = container.querySelector('input[name="price_max"]');
                if (el2) el2.value = v;
            }
            v = params.get('in_stock');
            var ch = container.querySelector('input[name="in_stock"]');
            if (ch) ch.checked = (v === '1');

            v = params.get('manufacturer_id');
            if (v) {
                var ids = v.split(',');
                ids.forEach(function(id){
                    var el = container.querySelector('input[name="manufacturer_id[]"][value="' + id + '"]');
                    if (el) el.checked = true;
                });
            }

            // селекти
            var s = document.getElementById('js-sort-select');
            if (s && params.get('sort')) s.value = params.get('sort');

            var o = document.getElementById('js-order-select');
            if (o && params.get('order')) o.value = params.get('order');
            var l = document.getElementById('js-limit-select');
            if (l && params.get('limit')) l.value = params.get('limit');
        },

        /**
         * Initialize dual-range price slider
         */
        initPriceSlider: function() {
            const sliderMin = document.getElementById('priceSliderMin');
            const sliderMax = document.getElementById('priceSliderMax');
            const inputMin = document.getElementById('priceInputMin');
            const inputMax = document.getElementById('priceInputMax');
            const track = document.getElementById('priceSliderTrack');
            const rangeMinLabel = document.querySelector('.price-range-min');
            const rangeMaxLabel = document.querySelector('.price-range-max');

            if (!sliderMin || !sliderMax || !inputMin || !inputMax || !track) return;

            // Получаване на min/max стойности от slider атрибутите
            const minPrice = parseInt(sliderMin.min) || 0;
            const maxPrice = parseInt(sliderMin.max) || 1000;

            // Получаване на валутната конфигурация от глобалната променлива
            const currencyConfig = window.currencyConfig || null;

            // Функция за форматиране на цена
            const formatPrice = function(amount) {
                if (currencyConfig && window.FrontendModule && window.FrontendModule.formatCurrency) {
                    return window.FrontendModule.formatCurrency(amount, currencyConfig);
                }
                return amount.toFixed(2) + ' лв.'; // Fallback
            };

            // Обновяване на range labels с валутно форматиране
            if (rangeMinLabel && currencyConfig) {
                rangeMinLabel.textContent = formatPrice(minPrice);
            }
            if (rangeMaxLabel && currencyConfig) {
                rangeMaxLabel.textContent = formatPrice(maxPrice);
            }

            // Функция за обновяване на track визуализацията
            const updateTrack = function() {
                const min = parseInt(sliderMin.value);
                const max = parseInt(sliderMax.value);
                const range = maxPrice - minPrice;

                const leftPercent = ((min - minPrice) / range) * 100;
                const rightPercent = ((max - minPrice) / range) * 100;

                track.style.left = leftPercent + '%';
                track.style.width = (rightPercent - leftPercent) + '%';
            };

            // Функция за синхронизация на стойности
            const syncValues = function() {
                let min = parseInt(sliderMin.value);
                let max = parseInt(sliderMax.value);

                // Осигуряване че min <= max
                if (min > max) {
                    if (this === sliderMin) {
                        sliderMax.value = min;
                        max = min;
                    } else {
                        sliderMin.value = max;
                        min = max;
                    }
                }

                // Обновяване на input полетата
                inputMin.value = min;
                inputMax.value = max;

                updateTrack();
            };

            // Event listeners за sliders
            sliderMin.addEventListener('input', syncValues);
            sliderMax.addEventListener('input', syncValues);

            // Event listeners за input полета
            const inputTimeout = {};
            const handleInputChange = function(inputElement, sliderElement) {
                clearTimeout(inputTimeout[inputElement.id]);
                inputTimeout[inputElement.id] = setTimeout(function() {
                    let value = parseInt(inputElement.value) || 0;
                    value = Math.max(minPrice, Math.min(maxPrice, value));

                    sliderElement.value = value;
                    inputElement.value = value;

                    syncValues.call(sliderElement);
                }, 300);
            };

            inputMin.addEventListener('input', function() {
                handleInputChange(inputMin, sliderMin);
            });

            inputMax.addEventListener('input', function() {
                handleInputChange(inputMax, sliderMax);
            });

            // Инициализация
            syncValues();
        }
    });

    // Export module to global scope
    window.ProductCategoryModule = ProductCategoryModule;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            if (window.ProductCategoryModule) window.ProductCategoryModule.init();
        });
    } else {
        if (window.ProductCategoryModule) window.ProductCategoryModule.init();
    }

})();

