<?php

namespace Theme25\Frontend\Controller\Startup;

class Startup extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'startup/startup');
    }

	public function index() {
		// Store
		if ($this->request->server['HTTPS']) {
			$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "store WHERE REPLACE(`ssl`, 'www.', '') = '" . $this->db->escape('https://' . str_replace('www.', '', $_SERVER['HTTP_HOST']) . rtrim(dirname($_SERVER['PHP_SELF']), '/.\\') . '/') . "'");
		} else {
			$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "store WHERE REPLACE(`url`, 'www.', '') = '" . $this->db->escape('http://' . str_replace('www.', '', $_SERVER['HTTP_HOST']) . rtrim(dirname($_SERVER['PHP_SELF']), '/.\\') . '/') . "'");
		}
		
		if (isset($this->request->get['store_id'])) {
			$this->config->set('config_store_id', (int)$this->request->get['store_id']);
		} else if ($query->num_rows) {
			$this->config->set('config_store_id', $query->row['store_id']);
		} else {
			$this->config->set('config_store_id', 0);
		}
		
		if (!$query->num_rows) {
			$this->config->set('config_url', HTTP_SERVER);
			$this->config->set('config_ssl', HTTPS_SERVER);
		}
		
		// Settings
		$query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "setting` WHERE store_id = '0' OR store_id = '" . (int)$this->config->get('config_store_id') . "' ORDER BY store_id ASC");
		
		foreach ($query->rows as $result) {
			if (!$result['serialized']) {
				$this->config->set($result['key'], $result['value']);
			} else {
				$this->config->set($result['key'], json_decode($result['value'], true));
			}
		}

		// Theme
		$this->config->set('template_cache', $this->config->get('developer_theme'));
		
		// Url
		$this->registry->set('url', new \Url($this->config->get('config_url'), $this->config->get('config_ssl')));
		
		// Language
		$code = '';
		
		$this->load->model('localisation/language');
		
		$languages = $this->model_localisation_language->getLanguages();
		
		if (isset($this->session->data['language'])) {
			$code = $this->session->data['language'];
		}
				
		if (isset($this->request->cookie['language']) && !array_key_exists($code, $languages)) {
			$code = $this->request->cookie['language'];
		}
		
		// Language Detection
		if (!empty($this->request->server['HTTP_ACCEPT_LANGUAGE']) && !array_key_exists($code, $languages)) {
			$detect = '';
			
			$browser_languages = explode(',', $this->request->server['HTTP_ACCEPT_LANGUAGE']);
			
			// Try using local to detect the language
			foreach ($browser_languages as $browser_language) {
				foreach ($languages as $key => $value) {
					if ($value['status']) {
						$locale = explode(',', $value['locale']);
						
						if (in_array($browser_language, $locale)) {
							$detect = $key;
							break 2;
						}
					}
				}	
			}			
			
			if (!$detect) { 
				// Try using language folder to detect the language
				foreach ($browser_languages as $browser_language) {
					if (array_key_exists(strtolower($browser_language), $languages)) {
						$detect = strtolower($browser_language);
						
						break;
					}
				}
			}
			
			$code = $detect ? $detect : '';
		}
		
		if (!array_key_exists($code, $languages)) {
			$code = $this->config->get('config_language');
		}
		
		if (!isset($this->session->data['language']) || $this->session->data['language'] != $code) {
			$this->session->data['language'] = $code;
		}
				
		if (!isset($this->request->cookie['language']) || $this->request->cookie['language'] != $code) {
			setcookie('language', $code, time() + 60 * 60 * 24 * 30, '/', $this->request->server['HTTP_HOST']);
		}
	
		// Overwrite the default language object
		$language = new \Language($code);
		$language->load($code);
		
		$this->registry->set('language', $language);
		
		// Set the config language_id
		$this->config->set('config_language_id', (!empty($languages[$code]['language_id']) ? $languages[$code]['language_id'] : 1));	

		// Customer
		$customer = new \Theme25\Customer($this->registry);
		$this->registry->set('customer', $customer);
		
		// Customer Group
		if (isset($this->session->data['customer']) && isset($this->session->data['customer']['customer_group_id'])) {
			// For API calls
			$this->config->set('config_customer_group_id', $this->session->data['customer']['customer_group_id']);
		} elseif ($this->customer->isLogged()) {
			// Logged in customers
			$this->config->set('config_customer_group_id', $this->customer->getGroupId());
		} elseif (isset($this->session->data['guest']) && isset($this->session->data['guest']['customer_group_id'])) {
			$this->config->set('config_customer_group_id', $this->session->data['guest']['customer_group_id']);
		}
		
		// Tracking Code
		if (isset($this->request->get['tracking'])) {
			setcookie('tracking', $this->request->get['tracking'], time() + 3600 * 24 * 1000, '/');
		
			$this->db->query("UPDATE `" . DB_PREFIX . "marketing` SET clicks = (clicks + 1) WHERE code = '" . $this->db->escape($this->request->get['tracking']) . "'");
		}		
		
		// Currency
		$code = '';
		
		$this->load->model('localisation/currency');
		
		$currencies = $this->model_localisation_currency->getCurrencies();
		
		if (isset($this->session->data['currency'])) {
			$code = $this->session->data['currency'];
		}
		
		if (isset($this->request->cookie['currency']) && !array_key_exists($code, $currencies)) {
			$code = $this->request->cookie['currency'];
		}
		
		if (!array_key_exists($code, $currencies)) {
			$code = $this->config->get('config_currency');
		}
		
		if (!isset($this->session->data['currency']) || $this->session->data['currency'] != $code) {
			$this->session->data['currency'] = $code;
		}
		
		if (!isset($this->request->cookie['currency']) || $this->request->cookie['currency'] != $code) {
			setcookie('currency', $code, time() + 60 * 60 * 24 * 30, '/', $this->request->server['HTTP_HOST']);
		}		
		
		$this->registry->set('currency', new \Theme25\Currency($this->registry));
		
		// Tax
		$this->registry->set('tax', new \Cart\Tax($this->registry));
		
		if (isset($this->session->data['shipping_address'])) {
			$this->tax->setShippingAddress($this->session->data['shipping_address']['country_id'], $this->session->data['shipping_address']['zone_id']);
		} elseif ($this->config->get('config_tax_default') == 'shipping') {
			$this->tax->setShippingAddress($this->config->get('config_country_id'), $this->config->get('config_zone_id'));
		}

		if (isset($this->session->data['payment_address'])) {
			$this->tax->setPaymentAddress($this->session->data['payment_address']['country_id'], $this->session->data['payment_address']['zone_id']);
		} elseif ($this->config->get('config_tax_default') == 'payment') {
			$this->tax->setPaymentAddress($this->config->get('config_country_id'), $this->config->get('config_zone_id'));
		}

		$this->tax->setStoreAddress($this->config->get('config_country_id'), $this->config->get('config_zone_id'));
		
		// Weight
		$this->registry->set('weight', new \Cart\Weight($this->registry));
		
		// Length
		$this->registry->set('length', new \Cart\Length($this->registry));
		
		// Cart - използваме Theme25\Cart с адаптерен модел за Backend/Frontend
		$this->registry->set('cart', new \Theme25\Cart($this->registry));
		
		// Encryption
		$this->registry->set('encryption', new \Encryption($this->config->get('config_encryption')));
		
		// OpenBay Pro
		// $this->registry->set('openbay', new \Openbay($this->registry));		
        
        $this->addCSPheaders();
	}

    public function addCSPheaders() {
        // Твоите базови правила
        $custom_csp = [
            "default-src" => ["'self'"],
            "script-src" => [
                "'self'",
                "'unsafe-inline'",
                "https://www.googletagmanager.com",
                "https://www.google-analytics.com",
                "https://maps.googleapis.com",
                "https://code.jquery.com",
                "https://google.com",
                "https://www.google.com",
                "https://www.gstatic.com",
                "https://connect.facebook.net",
                "https://www.googleadservices.com",
                "https://region1.analytics.google.com",
                "https://cdn.tiny.cloud",
                "https://pagead2.googlesyndication.com",
                "https://fundingchoicesmessages.google.com",
                "https://googleads.g.doubleclick.net",
                "https://tpc.googlesyndication.com",
                "https://adscout.io",
                "https://script.hotjar.com",
                "https://static.hotjar.com",
                "https://merchant.revolut.com",
                "https://cdn.commoninja.com"
            ],
            "style-src" => [
                "'self'",
                "'unsafe-inline'",
                "https://fonts.googleapis.com",
                "https://adscout.io",
                "https://cdn.tiny.cloud",
            ],
            "img-src" => [
                "'self'",
                "data:",
                "https:",
                "https://www.google-analytics.com",
            ],
            "font-src" => [
                "'self'",
                "data:",
                "https://fonts.gstatic.com"
            ],
            "connect-src" => [
                "'self'",
                "https://www.google-analytics.com",
                "https://region1.analytics.google.com",
                "https://google.com",
                "https://www.google.com",
                "https://region1.google-analytics.com",
                "https://maps.googleapis.com",
                "https://pagead2.googlesyndication.com",
                "https://googleads.g.doubleclick.net",
                "https://fundingchoicesmessages.google.com",
                "https://tpc.googlesyndication.com",
                "https://merchant.revolut.com",
                "https://cdn.tiny.cloud",
                "https://adscout.io",
                "wss://ws.hotjar.com",
                "https://content.hotjar.io",
                "https://metrics.hotjar.io",
                "https://vc.hotjar.io",
                "https://cdn.commoninja.com"
            ],
            "frame-src" => [
                "'self'",
                "https://www.google.com",
                "https://www.facebook.com",
                "https://*.doubleclick.net",
                "https://googleads.g.doubleclick.net",
                "https://fundingchoicesmessages.google.com",
                "https://tpc.googlesyndication.com",
                "https://www.googletagmanager.com",
                "https://merchant.revolut.com",
                "https://adscout.io",
                "https://widget-v4.boxnow.bg",
                "https://www.youtube.com",
                "https://youtube.com"
            ],
            "form-action" => [
                "'self'",
                "https://www.facebook.com"
            ],
            "object-src" => ["'none'"],
            "base-uri" => ["'self'"],
            "frame-ancestors" => ["'none'"],
            "upgrade-insecure-requests" => true
        ];
    
        // Автоматичен whitelist с често използвани CDN-и
        $cdn_whitelist = [
            "script-src" => [
                "https://cdn.tailwindcss.com",
                "https://cdn.jsdelivr.net"
            ],
            "style-src" => [
                "https://cdn.jsdelivr.net"
            ],
            "font-src" => [
                "https://cdn.jsdelivr.net"
            ]
        ];
    
        // Merge: добавяме CDN-ите само ако ги няма
        foreach ($cdn_whitelist as $directive => $urls) {
            if (!isset($custom_csp[$directive])) {
                $custom_csp[$directive] = [];
            }
            foreach ($urls as $url) {
                if (!in_array($url, $custom_csp[$directive])) {
                    $custom_csp[$directive][] = $url;
                }
            }
        }
    
        // Permissions Policy
        $permissions_policy = [
            'geolocation' => 'self',
            'microphone' => false,
            'camera' => false,
            'payment' => 'self',
            'fullscreen' => true,
        ];
    
        // Сглобяване на CSP низа
        $csp_parts = [];
        foreach ($custom_csp as $directive => $value) {
            if ($value === true) {
                $csp_parts[] = $directive;
            } elseif ($value !== false) {
                $csp_parts[] = $directive . ' ' . implode(' ', (array)$value);
            }
        }
        $csp_header = implode('; ', $csp_parts);
    
        // Сглобяване на Permissions-Policy низа
        $pp_parts = [];
        foreach ($permissions_policy as $feature => $setting) {
            if ($setting === true) {
                $pp_parts[] = $feature."=*";
            } elseif ($setting === false) {
                $pp_parts[] = $feature."=()";
            } else {
                $pp_parts[] = $feature."=(".$setting.")";
            }
        }
        $pp_header = implode(', ', $pp_parts);

        // Добавяне на хедърите
        $this->response->addHeader("Content-Security-Policy: " . $csp_header);
        $this->response->addHeader("Permissions-Policy: " . $pp_header);
    }
    
    
}
