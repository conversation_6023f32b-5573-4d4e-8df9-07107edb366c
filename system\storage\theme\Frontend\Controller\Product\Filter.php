<?php

namespace Theme25\Frontend\Controller\Product;

/**
 * AJAX контролер за филтриране на продукти (route: product/filter)
 */
class Filter extends Cards
{
    public function __construct($registry)
    {
        parent::__construct($registry, 'product/filter');
        if (\is_callable([$this, 'loadScripts'])) {
            $this->loadScripts(['cart']);
        }
    }

    public function index()
    {
        // Само AJAX заявки за филтърни опции
        if (\is_callable([$this, 'isAjaxRequest']) && !$this->isAjaxRequest()) {
            return $this->jsonResponse(['success' => false, 'error' => 'Невалидна заявка']);
        }

        // Параметри
        $category_id = $this->getCategoryIdFromRequest();
        if ($category_id <= 0) {
            return $this->jsonResponse(['success' => false, 'error' => 'Невалидна категория']);
        }

        // Модел за филтриране
        $this->loadModelAs('catalog/product/filter', 'productFilterModel');

        // Филтърни опции за AJAX заявки от sidebar
        $filter_options = $this->productFilterModel->getFilterOptions($category_id);

        return $this->jsonResponse([
            'success' => true,
            'filters' => $filter_options,
        ]);
    }

    private function getCategoryIdFromRequest(): int
    {
        $path = (string)($this->requestGet('path') ?: '');
        if ($path !== '') {
            $parts = explode('_', $path); $last = end($parts); return ctype_digit((string)$last) ? (int)$last : 0;
        }
        return (int)$this->requestGet('category_id');
    }


}

