<?php

namespace Theme25;

/**
 * Клас за генериране на пагинация в съответствие с дизайна на темата
 */
class Pagination {
    /**
     * @var int Общ брой елементи
     */
    public $total = 0;

    /**
     * @var int Текуща страница
     */
    public $page = 1;

    /**
     * @var int Брой елементи на страница
     */
    public $limit = 20;

    /**
     * @var int Брой връзки за показване (без първа и последна)
     */
    public $num_links = 5;

    /**
     * @var string URL шаблон за страниците
     */
    public $url = '';

    /**
     * @var string URL шаблон за промяна на броя елементи на страница
     */
    public $limit_url = '';

    /**
     * @var array Масив с опции за брой елементи на страница
     */
    public $limits = [];

    /**
     * @var string Текст за продуктите (например "продукта", "артикула" и т.н.)
     */
    public $product_text = 'продукта';

    /**
     * Генерира HTML код за пагинацията в съответствие с дизайна на темата
     *
     * @return string HTML код за пагинацията
     */
    // private function renderPagination() {
    //     // Изчисляване на общия брой страници
    //     $num_pages = max(1, ceil($this->total / $this->limit));

    //     // Ако има само една страница, не показваме пагинация
    //     if ($num_pages <= 1) {
    //         return '';
    //     }

    //     // Нормализиране на текущата страница
    //     $page = max(1, min($this->page, $num_pages));

    //     // Подготовка на URL адреса
    //     $url = str_replace('%7Bpage%7D', '{page}', $this->url);

    //     // Запазване на текущите query параметри
    //     $current_url = $_SERVER['REQUEST_URI'];
    //     $query_params = [];

    //     // Извличане на query параметрите от текущия URL
    //     $url_parts = parse_url($current_url);
    //     if (isset($url_parts['query'])) {
    //         parse_str($url_parts['query'], $query_params);
    //     }

    //     // Премахване на параметрите, които вече са включени в базовия URL
    //     // или които ще бъдат добавени отново
    //     $exclude_params = ['page', 'route', '_route_', 'user_token'];
    //     foreach ($exclude_params as $param) {
    //         if (isset($query_params[$param])) {
    //             unset($query_params[$param]);
    //         }
    //     }

    //     // Начало на HTML кода
    //     $output = '<div class="flex theme-pagination">';

    //     // Добавяне на текущите query параметри към URL адреса
    //     $query_string = '';
    //     if (!empty($query_params)) {
    //         $query_string = '&' . http_build_query($query_params);
    //     }

    //     // Бутон "Предишна страница"
    //     if ($page > 1) {
    //         if ($page - 1 === 1) {
    //             $prev_url = str_replace(array('&amp;page={page}', '?page={page}', '&page={page}'), '', $url);
    //             // Добавяне на текущите query параметри
    //             if (strpos($prev_url, '?') !== false) {
    //                 $prev_url .= $query_string;
    //             } else if (!empty($query_string)) {
    //                 $prev_url .= '?' . substr($query_string, 1); // Премахване на началния '&'
    //             }
    //             $output .= '<a href="' . $prev_url . '" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100">';
    //         } else {
    //             $prev_url = str_replace('{page}', $page - 1, $url);
    //             // Добавяне на текущите query параметри
    //             if (strpos($prev_url, '?') !== false) {
    //                 $prev_url .= $query_string;
    //             } else if (!empty($query_string)) {
    //                 $prev_url .= '?' . substr($query_string, 1); // Премахване на началния '&'
    //             }
    //             $output .= '<a href="' . $prev_url . '" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100">';
    //         }
    //         $output .= '<div class="w-5 h-5 flex items-center justify-center">';
    //         $output .= '<i class="ri-arrow-left-s-line"></i>';
    //         $output .= '</div>';
    //         $output .= '</a>';
    //     } else {
    //         $output .= '<button disabled class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">';
    //         $output .= '<div class="w-5 h-5 flex items-center justify-center">';
    //         $output .= '<i class="ri-arrow-left-s-line"></i>';
    //         $output .= '</div>';
    //         $output .= '</button>';
    //     }

    //     // Определяне на диапазона от страници за показване
    //     $num_links = $this->num_links;

    //     if ($num_pages <= $num_links) {
    //         $start = 1;
    //         $end = $num_pages;
    //     } else {
    //         $start = $page - floor($num_links / 2);
    //         $end = $page + floor($num_links / 2);

    //         if ($start < 1) {
    //             $end += abs($start) + 1;
    //             $start = 1;
    //         }

    //         if ($end > $num_pages) {
    //             $start -= ($end - $num_pages);
    //             $end = $num_pages;
    //         }

    //         $start = max(1, $start);
    //     }

    //     // Показване на номерата на страниците
    //     for ($i = $start; $i <= $end; $i++) {
    //         if ($page == $i) {
    //             $output .= '<button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-primary text-white">' . $i . '</button>';
    //         } else {
    //             if ($i === 1) {
    //                 $page_url = str_replace(array('&amp;page={page}', '?page={page}', '&page={page}'), '', $url);
    //                 // Добавяне на текущите query параметри
    //                 if (strpos($page_url, '?') !== false) {
    //                     $page_url .= $query_string;
    //                 } else if (!empty($query_string)) {
    //                     $page_url .= '?' . substr($query_string, 1); // Премахване на началния '&'
    //                 }
    //                 $output .= '<a href="' . $page_url . '" class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">' . $i . '</a>';
    //             } else {
    //                 $page_url = str_replace('{page}', $i, $url);

    //                 // Добавяне на текущите query параметри
    //                 if (strpos($page_url, '?') !== false) {
    //                     $page_url .= $query_string;
    //                 } else if (!empty($query_string)) {
    //                     $page_url .= '?' . substr($query_string, 1); // Премахване на началния '&'
    //                 }
    //                 $output .= '<a href="' . $page_url . '" class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">' . $i . '</a>';
    //             }
    //         }
    //     }

    //     // Бутон "Следваща страница"
    //     if ($page < $num_pages) {
    //         $next_url = str_replace('{page}', $page + 1, $url);
    //         // Добавяне на текущите query параметри
    //         if (strpos($next_url, '?') !== false) {
    //             $next_url .= $query_string;
    //         } else if (!empty($query_string)) {
    //             $next_url .= '?' . substr($query_string, 1); // Премахване на началния '&'
    //         }
    //         $output .= '<a href="' . $next_url . '" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100">';
    //         $output .= '<div class="w-5 h-5 flex items-center justify-center">';
    //         $output .= '<i class="ri-arrow-right-s-line"></i>';
    //         $output .= '</div>';
    //         $output .= '</a>';
    //     } else {
    //         $output .= '<button disabled class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">';
    //         $output .= '<div class="w-5 h-5 flex items-center justify-center">';
    //         $output .= '<i class="ri-arrow-right-s-line"></i>';
    //         $output .= '</div>';
    //         $output .= '</button>';
    //     }

    //     // Край на HTML кода
    //     $output .= '</div>';

    //     return $output;
    // }

    private function renderPagination() {
        // 1. Изчисляване на общия брой страници
        $num_pages = max(1, ceil($this->total / $this->limit));
    
        // Ако има само една страница, не показваме нищо
        if ($num_pages <= 1) {
            return '';
        }
    
        // 2. Нормализиране на текущата страница
        $page = max(1, min((int)$this->page, $num_pages));
    
        // 3. Подготовка на базовия URL и параметрите
        // Разделяме базовия URL на път и параметри
        $url_parts = parse_url(str_replace('&amp;', '&', $this->url));
        $base_path = $url_parts['path'] ?? '';
    
        // Взимаме параметрите от базовия URL и ги обединяваме с текущите GET параметри.
        // Това запазва всички филтри, сортирания и т.н. от текущия request.
        $base_params = [];
        if (isset($url_parts['query'])) {
            parse_str($url_parts['query'], $base_params);
        }
        $final_params = array_merge($base_params, $_GET);
    
        // Премахваме параметрите, които не трябва да се повтарят
        unset($final_params['page']);
        unset($final_params['route']); // Тези бяха във вашия оригинален код
        unset($final_params['_route_']);
        unset($final_params['user_token']);
    
        // 4. Създаваме помощна функция за генериране на URL
        // Това елиминира повторението на код
        $generateUrl = function($page_number) use ($base_path, $final_params) {
            $params = $final_params;
    
            // Добавяме параметъра 'page' само ако страницата не е първа (за по-чист URL)
            if ($page_number > 1) {
                $params['page'] = $page_number;
            }
    
            // Връщаме пълния URL
            return $base_path . (!empty($params) ? '?' . http_build_query($params) : '');
        };
    
        // 5. Генериране на HTML
        $output = '<div class="flex theme-pagination">';
    
        // Бутон "Предишна страница"
        if ($page > 1) {
            $output .= '<a href="' . $generateUrl($page - 1) . '" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100">';
            $output .= '<div class="w-5 h-5 flex items-center justify-center"><i class="ri-arrow-left-s-line"></i></div></a>';
        } else {
            $output .= '<button disabled class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">';
            $output .= '<div class="w-5 h-5 flex items-center justify-center"><i class="ri-arrow-left-s-line"></i></div></button>';
        }
    
        // Определяне на диапазона от страници за показване (логиката е запазена)
        $num_links = $this->num_links;
        if ($num_pages <= $num_links) {
            $start = 1;
            $end = $num_pages;
        } else {
            $start = $page - floor($num_links / 2);
            $end = $page + floor($num_links / 2);
            if ($start < 1) {
                $end += abs($start) + 1;
                $start = 1;
            }
            if ($end > $num_pages) {
                $start -= ($end - $num_pages);
                $end = $num_pages;
            }
            $start = max(1, $start);
        }
    
        // Показване на номерата на страниците
        for ($i = $start; $i <= $end; $i++) {
            if ($page == $i) {
                $output .= '<button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-primary text-white">' . $i . '</button>';
            } else {
                $output .= '<a href="' . $generateUrl($i) . '" class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">' . $i . '</a>';
            }
        }
    
        // Бутон "Следваща страница"
        if ($page < $num_pages) {
            $output .= '<a href="' . $generateUrl($page + 1) . '" class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100">';
            $output .= '<div class="w-5 h-5 flex items-center justify-center"><i class="ri-arrow-right-s-line"></i></div></a>';
        } else {
            $output .= '<button disabled class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">';
            $output .= '<div class="w-5 h-5 flex items-center justify-center"><i class="ri-arrow-right-s-line"></i></div></button>';
        }
    
        $output .= '</div>';
    
        return $output;
    }

    /**
     * Генерира информация за резултатите
     *
     * @return string Информация за резултатите
     */
    private function renderResultsText() {
        // Ако няма резултати, показваме 0-0 от 0
        if ($this->total == 0) {
            return sprintf('Показани 0-0 от 0 %s', $this->product_text);
        }

        $start = (($this->page - 1) * $this->limit) + 1;
        $end = min($this->total, $this->page * $this->limit);

        return sprintf('Показани %d-%d от %d %s', $start, $end, $this->total, $this->product_text);
    }

    /**
     * Генерира HTML код за падащото меню за брой елементи на страница
     *
     * @return string HTML код за падащото меню
     */
    private function renderLimitDropdown() {
        if (empty($this->limits)) {
            return '';
        }

        // Подготовка на URL адреса
        $url = str_replace('%7Blimit%7D', '{limit}', $this->limit_url);

        // Запазване на текущите query параметри
        $current_url = $_SERVER['REQUEST_URI'];
        $query_params = [];

        // Извличане на query параметрите от текущия URL
        $url_parts = parse_url($current_url);
        if (isset($url_parts['query'])) {
            parse_str($url_parts['query'], $query_params);
        }

        // Премахване на параметрите, които вече са включени в базовия URL
        // или които ще бъдат добавени отново
        // ВАЖНО: Премахваме и 'page' за да започнем от първа страница при смяна на limit
        $exclude_params = ['limit', 'page', 'route', 'user_token'];
        foreach ($exclude_params as $param) {
            if (isset($query_params[$param])) {
                unset($query_params[$param]);
            }
        }

        // Добавяне на текущите query параметри към URL адреса
        $query_string = '';
        if (!empty($query_params)) {
            $query_string = '&' . http_build_query($query_params);
        }

        // Начало на HTML кода
        $output = '<div class="relative">';
        $output .= '<button id="per-page-dropdown-btn" class="flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">';
        $output .= '<span>' . $this->limit . ' на страница</span>';
        $output .= '<div class="w-5 h-5 flex items-center justify-center ml-2">';
        $output .= '<i class="ri-arrow-down-s-line"></i>';
        $output .= '</div>';
        $output .= '</button>';
        $output .= '<div id="per-page-dropdown" class="hidden absolute right-0 z-10 w-40 mt-1 bg-white border border-gray-200 rounded shadow-lg text-sm">';
        $output .= '<ul class="py-1">';

        foreach ($this->limits as $limit) {
            $limit_url = str_replace('{limit}', $limit['value'], $url);
            // Добавяне на текущите query параметри
            if (strpos($limit_url, '?') !== false) {
                $limit_url .= $query_string;
            } else if (!empty($query_string)) {
                $limit_url .= '?' . substr($query_string, 1); // Премахване на началния '&'
            }
            $output .= '<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-limit="' . $limit['value'] . '" data-url="' . $limit_url . '">' . $limit['text'] . '</li>';
        }

        $output .= '</ul>';
        $output .= '</div>';
        $output .= '</div>';

        // JavaScript код за функционалност на падащото меню
        $output .= <<<'JAVASCRIPT'
<script type="text/javascript">
document.addEventListener("DOMContentLoaded", function() {
    // Изчакваме малко, за да сме сигурни, че DOM е напълно зареден
    setTimeout(function() {
        const perPageDropdownBtn = document.getElementById("per-page-dropdown-btn");
        const perPageDropdown = document.getElementById("per-page-dropdown");

        if (!perPageDropdownBtn || !perPageDropdown) {
            return;
        }

        // Променлива за проследяване на състоянието на менюто
        let menuVisible = false;

        // Обработчик за клик върху бутона
        perPageDropdownBtn.addEventListener("click", function(e) {
            e.preventDefault();
            e.stopImmediatePropagation(); // Спираме незабавното разпространение на събитието

            // Превключваме състоянието
            menuVisible = !menuVisible;

            // Прилагаме новото състояние
            if (menuVisible) {
                // Показваме менюто
                perPageDropdown.style.display = "block";
                perPageDropdown.classList.remove("hidden");
            } else {
                // Скриваме менюто
                perPageDropdown.style.display = "none";
                perPageDropdown.classList.add("hidden");
            }

            // Предотвратяваме разпространението на събитието
            return false;
        });

        // Добавяме обработчик за клик извън менюто със забавяне
        setTimeout(function() {
            document.addEventListener("click", function(event) {
                // Проверяваме дали кликът е извън бутона и менюто
                if (menuVisible &&
                    !perPageDropdownBtn.contains(event.target) &&
                    !perPageDropdown.contains(event.target)) {

                    perPageDropdown.style.display = "none";
                    perPageDropdown.classList.add("hidden");
                    menuVisible = false;
                }
            });
        }, 200); // Малко забавяне, за да не се задейства веднага

        // Добавяме обработчици на събитията за елементите в менюто
        const perPageItems = perPageDropdown.querySelectorAll("li");
        perPageItems.forEach(item => {
            item.addEventListener("click", function() {
                const url = this.getAttribute("data-url");
                window.location.href = url;
            });
        });

        // Задаваме начално състояние - скрито
        perPageDropdown.style.display = "none";
        perPageDropdown.classList.add("hidden");
        menuVisible = false;

    }, 100);
});
</script>
JAVASCRIPT;

        return $output;
    }

    /**
     * Генерира HTML код за цялата пагинация (резултати, падащо меню и бутони за страниците)
     *
     * @return string HTML код за цялата пагинация
     */
    public function render() {
        // Ако няма резултати, не показваме пагинация
        if ($this->total == 0) {
            return '';
        }

        // $output = '<div class="mt-8 flex items-center justify-between">';
        $output = '<div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">';
        $output .= '<div class="text-sm text-gray-600 pr-4">';
        $output .= $this->renderResultsText();
        $output .= '</div>';
        $output .= '<div class="flex items-center gap-4">';

        // Падащо меню за брой елементи на страница
        if (!empty($this->limits)) {
            $output .= $this->renderLimitDropdown();
        }

        // Бутони за страниците
        $pagination = $this->renderPagination();
        if (!empty($pagination)) {
            $output .= $pagination;
        }

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Задава опциите за брой елементи на страница
     *
     * @param array $limits Масив с опции за брой елементи на страница
     * @return $this За верижно извикване на методи
     */
    public function setLimits($limits) {
        $this->limits = $limits;
        return $this;
    }

    /**
     * Задава URL шаблон за промяна на броя елементи на страница
     *
     * @param string $url URL шаблон за промяна на броя елементи на страница
     * @return $this За верижно извикване на методи
     */
    public function setLimitUrl($url) {
        $this->limit_url = $url;
        return $this;
    }

    /**
     * Задава текст за продуктите
     *
     * @param string $text Текст за продуктите
     * @return $this За верижно извикване на методи
     */
    public function setProductText($text) {
        $this->product_text = $text;
        return $this;
    }

    public function setResultsText($text) {
        $this->product_text = $text;
        return $this;
    }


}
