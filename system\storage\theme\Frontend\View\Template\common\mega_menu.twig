{#
/**
 * Динамичен мегаменю шаблон
 * 
 * @param menu_items array - Масив с елементите на менюто
 * @param container_class string - CSS класове за контейнера (по подразбиране: 'menu-container mt-4')
 * @param nav_class string - CSS класове за навигацията (по подразбиране: 'flex space-x-8')
 */
#}

{# Импортиране на макроси #}
{% include 'common/_macros/menu-macros.twig' %}


<div class="{{ container_class|default('menu-container mt-4') }}">
    <nav>
        <ul class="{{ nav_class|default('flex space-x-8') }}">
            {% for item in menu_items %}
                {% set has_submenu = item.submenu is defined and item.submenu %}
                {% set item_slug = item.slug|default(item.title|lower|replace({' ': '-'})) %}
                {% set is_active = active_menu_slug and active_menu_slug == item_slug %}

                <li class="menu-item group{{ has_submenu ? ' has-submenu' : '' }}{{ is_active ? ' active' : '' }}">
                    <a href="{{ item.url }}"
                       class="{{ has_submenu ? 'flex items-center' : 'block' }} py-2 font-medium hover:text-primary{{ is_active ? ' text-primary active' : '' }} {{ item.class|default('') }}">
                        {{ item.title }}
                        {% if has_submenu %}
                            <i class="ri-arrow-down-s-line ml-1 text-gray-400"></i>
                        {% endif %}
                    </a>
                </li>
            {% endfor %}
        </ul>
    </nav>

    {# Контейнери за подменютата #}
    <div class="mega-menu-container mt-0">
        {% for item in menu_items %}
            {% if item.submenu is defined and item.submenu %}
                {% set submenu = item.submenu %}
                
                <div class="bg-white shadow-lg py-8 hidden group-hover:block" 
                     data-menu="{{ item.slug|default(item.title|lower|replace({' ': '-'})) }}">
                    
                    {% if submenu.type == 'categories' %}
                        {% include 'common/mega-menu/categories.twig' with {'submenu': submenu} %}
                    
                    {% elseif submenu.type == 'promo' %}
                        {% include 'common/mega-menu/promo.twig' with {'submenu': submenu} %}
                    
                    {% elseif submenu.type == 'gallery' %}
                        {% include 'common/mega-menu/gallery.twig' with {'submenu': submenu} %}
                    
                    {% elseif submenu.type == 'mixed' %}
                        {% include 'common/mega-menu/mixed.twig' with {'submenu': submenu} %}
                    {% endif %}
                    
                </div>
            {% endif %}
        {% endfor %}
    </div>
    

    {# Overlay backdrop #}
    <div class="overlay-backdrop fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

</div>