# Потребителски промпт
Има критични проблеми с JavaScript архитектурата и функционалността в product-category.js файла, които трябва да се поправят:

1) Архитектурен проблем - неправилно наследяване от FrontendModule
2) Липсваща функционалност за product labels
3) Неработещ "Купи" бутон

Приоритет: 1) Архитектура, 2) Labels данни, 3) Интеграция с CartModule

# Изпълнени стъпки

- Създадени резервни копия (PowerShell):
  - system/storage/theme/Frontend/View/Javascript/product-category_YYYY-MM-DD_HHmm.js

- Поправки в system/storage/theme/Frontend/View/Javascript/product-category.js:
  1) Архитектура (FrontendModule pattern):
     - Приложен същият pattern както в cart.js:
       - const ProductCategoryModule = Object.create(window.FrontendModule || {});
       - Object.assign(ProductCategoryModule, { init, fetchFiltered, updateGrid, serializeFilters, bindEvents });
       - window.ProductCategoryModule = ProductCategoryModule;
     - DOM инициализацията извиква ProductCategoryModule.init().

  2) Product labels:
     - Адаптирано рендиране към формата, използван във Featured:
       - p.labels е масив от обекти { class, text }.
       - Рендиране в позиция absolute top-2 left-2, с итериране по labels и изграждане на span елементи.
     - Причина за липсата преди: кодът очакваше p.labels.special/new/out_of_stock, а реално идва масив от Theme25\ProductLabels чрез prepareProductCardData().

  3) Бутон „Купи” и CartModule:
     - HTML на картите е синхронизиран с Featured:
       - data-product-id, data-product-options, data-product-info са върху <a class="group"> контейнера.
       - Бутонът е с клас buyButton.
     - CartModule вече обработва клика (document-level delegation) и предотвратява навигацията.
     - След AJAX обновяване се вика bindSlideCartEvents() само за UI на slide cart (не е задължително за buyButton, но е безопасно).

- Потвърдено в PHP:
  - Контролер Category.php използва prepareProductCardData() => подава labels.
  - AJAX контролер product/filter също използва prepareProductCardData() => labels присъстват в JSON.
  - Не са нужни промени по PHP, защото данните вече са налице.

# Ключови извадки

- Регистрация на модула:
```
const ProductCategoryModule = Object.create(window.FrontendModule || {});
Object.assign(ProductCategoryModule, {
  init: function(){ applyUrlToFilters(); bindEvents(); },
  fetchFiltered: fetchFiltered,
  updateGrid: updateGrid,
  serializeFilters: serializeFilters,
  bindEvents: bindEvents
});
window.ProductCategoryModule = ProductCategoryModule;
```

- Рендиране на labels (в updateGrid):
```
(function(){
  var _s='';
  if (p.labels && Array.isArray(p.labels) && p.labels.length) {
    _s += '      <div class="absolute top-2 left-2 space-y-1">';
    p.labels.forEach(function(lb){
      var cls = lb.class || 'bg-primary text-white text-sm px-2 py-1 rounded';
      var txt = lb.text || '';
      _s += '        <span class="'+cls+'">'+txt+'</span>';
    });
    _s += '      </div>';
  }
  return _s;
})()
```

# Потенциални следващи стъпки
- UI подобрения за селектите (custom dropdown) по стила на Theme25\Pagination с клавиатурна навигация.
- Дребни визуални корекции, ако класовете от labels изискват специфични Tailwind утилити класове.

# Забележки
- Не са правени промени по други файлове.
- Изпълнени са всички изисквания за архивиране преди промени.

