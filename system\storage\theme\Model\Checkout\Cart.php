<?php

namespace Theme25\Model\Checkout;

class Cart extends \Theme25\Model {

    private $cart_session;

    public function __construct($registry) {
        parent::__construct($registry, true);
        $this->initCartSession();
    }

    private function initCartSession() {
        $this->cart_session = $this->registry->get('cart_session');
        if(!$this->cart_session){
            // Инициализация на CartSession с OpenCart сесия
            $customer_id = $this->customer->isLogged() ? $this->customer->getId() : 0;
            $this->cart_session = new \Theme25\CartSession($this->db, null, $customer_id, null, $this->session);
            $this->registry->set('cart_session', $this->cart_session);

            // Синхронизация с oc_cart
            $this->cart_session->syncWithCart($this->cart->getProducts());
        }     
    }

    /**
     * Получава всички продукти в количката с подробна информация
     *
     * @return array Масив с продукти в количката
     */
    public function getCartProducts() {
        $products = [];
        $cart_products = $this->cart->getProducts();

        foreach ($cart_products as $cart_product) {
            $product_info = $this->getProduct($cart_product['product_id']);
            
            if ($product_info) {
                $product = [
                    'cart_id' => $cart_product['cart_id'],
                    'product_id' => $cart_product['product_id'],
                    'name' => $product_info['name'],
                    'model' => $product_info['model'] ?? '',
                    'description' => $product_info['description'] ?? '',
                    'image' => $product_info['image'] ?? '',
                    'price' => (float)$cart_product['price'],
                    'special' => $product_info['special'] ? (float)$product_info['special'] : null,
                    'quantity' => (int)$cart_product['quantity'],
                    'minimum' => (int)$product_info['minimum'],
                    'maximum' => (int)$product_info['maximum'],
                    'stock_status' => $product_info['stock_status'] ?? '',
                    'stock_quantity' => (int)$product_info['quantity'],
                    'subtract' => (bool)$product_info['subtract'],
                    'total' => (float)$cart_product['total'],
                    'weight' => (float)$product_info['weight'],
                    'weight_class' => $product_info['weight_class'] ?? '',
                    'length' => (float)$product_info['length'],
                    'width' => (float)$product_info['width'],
                    'height' => (float)$product_info['height'],
                    'length_class' => $product_info['length_class'] ?? '',
                    'options' => $cart_product['option'] ?? [],
                    'recurring' => $cart_product['recurring'] ?? null,
                    'href' => $this->url->link('product/product', 'product_id=' . $cart_product['product_id'])
                ];

                // Обработка на опциите
                if (!empty($cart_product['option'])) {
                    $product['options_formatted'] = $this->formatProductOptions($cart_product['product_id'], $cart_product['option']);
                } else {
                    $product['options_formatted'] = [];
                }

                // Обработка на изображението
                if (!empty($product_info['image'])) {
                    try {
                        $this->loadModelAs('tool/image', 'imageModel');
                        $product['thumb'] = $this->imageModel->resize($product_info['image'], 100, 100);
                        $product['thumb_medium'] = $this->imageModel->resize($product_info['image'], 200, 200);
                        $product['thumb_large'] = $this->imageModel->resize($product_info['image'], 400, 400);
                    } catch (\Exception $e) {
                        $product['thumb'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                        $product['thumb_medium'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                        $product['thumb_large'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                    }
                } else {
                    $product['thumb'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                    $product['thumb_medium'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                    $product['thumb_large'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                }

                $products[] = $product;
            }
        }

        return $products;
    }

    /**
     * Получава информация за продукт
     *
     * @param int $product_id ID на продукта
     * @return array|false Информация за продукта или false
     */
    public function getProduct($product_id) {
        $this->loadModelAs('catalog/product', 'productModel');
        return $this->productModel->getProduct($product_id);
    }

    /**
     * Форматира опциите на продукт за показване
     *
     * @param int $product_id ID на продукта
     * @param array $selected_options Избрани опции
     * @return array Форматирани опции
     */
    public function formatProductOptions($product_id, $selected_options) {
        $formatted_options = [];
        
        $this->loadModelAs('catalog/product', 'productModel');
        $product_options = $this->productModel->getProductOptions($product_id);

        foreach ($product_options as $product_option) {
            $option_id = $product_option['product_option_id'];
            
            if (isset($selected_options[$option_id])) {
                $option_data = [
                    'product_option_id' => $option_id,
                    'name' => $product_option['name'],
                    'value' => '',
                    'type' => $product_option['type']
                ];
                
                if (in_array($product_option['type'], ['select', 'radio', 'checkbox'])) {
                    // За опции с предварително дефинирани стойности
                    $selected_values = is_array($selected_options[$option_id]) ? $selected_options[$option_id] : [$selected_options[$option_id]];
                    $value_names = [];
                    
                    foreach ($product_option['product_option_value'] as $option_value) {
                        if (in_array($option_value['product_option_value_id'], $selected_values)) {
                            $value_names[] = $option_value['name'];
                        }
                    }
                    
                    $option_data['value'] = implode(', ', $value_names);
                } else {
                    // За текстови опции
                    $option_data['value'] = $selected_options[$option_id];
                }
                
                $formatted_options[] = $option_data;
            }
        }

        return $formatted_options;
    }

    /**
     * Получава обобщение на количката с интеграция на CartSession
     *
     * @return array Обобщение с общи суми, данъци, доставка и т.н.
     */
    public function getCartTotals() {

        // Получаване на продуктите в количката
        $cart_products = $this->cart->getProducts();

        // Подготовка на данните за продуктите за calculateTotals
        $products_data = [];
        foreach ($cart_products as $cart_product) {
            $products_data[] = [
                'product_id' => $cart_product['product_id'],
                'name' => $cart_product['name'],
                'model' => $cart_product['model'],
                'quantity' => $cart_product['quantity'],
                'price' => $cart_product['price'],
                'total' => $cart_product['total'],
                'tax_class_id' => $cart_product['tax_class_id'] ?? 0,
                'weight' => $cart_product['weight'] ?? 0,
                'weight_class_id' => $cart_product['weight_class_id'] ?? 0,
                'length' => $cart_product['length'] ?? 0,
                'width' => $cart_product['width'] ?? 0,
                'height' => $cart_product['height'] ?? 0,
                'length_class_id' => $cart_product['length_class_id'] ?? 0,
                'subtract' => $cart_product['subtract'] ?? true,
                'minimum' => $cart_product['minimum'] ?? 1,
                'option' => $cart_product['option'] ?? []
            ];
        }

        // Използване на calculateTotals метода от Total модела
        if (!empty($products_data)) {
            $this->loadModelAs('extension/total', 'totalModel');

            // Прилагане на coupons и vouchers от CartSession преди изчисляване на тоталите
            $this->applyCouponsAndVouchersToSession($products_data);

            // Изчисляване на тоталите с CartSession
            $calculated_totals = $this->totalModel->calculateTotals($products_data, null, 'detailed');
            // $calculated_totals = [];

            // Форматиране на резултата
            $totals = [];
            $total = 0;

            foreach ($calculated_totals as $total_data) {
                // Форматиране на текста с валута
                $total_data['text'] = $this->formatCurrency($total_data['value']);
                $totals[] = $total_data;

                // Изчисляване на крайната сума
                if ($total_data['code'] === 'total') {
                    $total = $total_data['value'];
                }
            }

            // Сортиране на totals по sort_order
            usort($totals, function($a, $b) {
                return ($a['sort_order'] ?? 0) - ($b['sort_order'] ?? 0);
            });

            return [
                'totals' => $totals,
                // 'taxes' => $this->cart->getTaxes(),
                'taxes' => [],
                'total' => $total,
            ];
        } else {
            // Празна количка
            return [
                'totals' => [],
                'taxes' => [],
                'total' => 0,
            ];
        }
    }

    /**
     * Проверява дали количката е празна
     *
     * @return bool True ако количката е празна
     */
    public function isEmpty() {
        return $this->cart->countProducts() == 0;
    }

    /**
     * Получава броя на продуктите в количката
     *
     * @return int Брой продукти
     */
    public function getProductCount() {
        return $this->cart->countProducts();
    }

    /**
     * Получава общото тегло на количката
     *
     * @return float Общо тегло
     */
    public function getTotalWeight() {
        return $this->cart->getWeight();
    }

    /**
     * Проверява дали има минимална поръчка
     *
     * @return array Резултат с информация за минималната поръчка
     */
    public function checkMinimumOrder() {
        $minimum_order = $this->config->get('config_minimum_order');
        $cart_total = $this->cart->getSubTotal();
        
        return [
            'minimum_required' => (float)$minimum_order,
            'current_total' => $cart_total,
            'is_valid' => $cart_total >= $minimum_order,
            'difference' => $minimum_order - $cart_total
        ];
    }

    /**
     * Получава информация за доставка
     *
     * @return array Информация за доставка
     */
    public function getShippingInfo() {
        //$free_shipping_total = $this->config->get('config_free_shipping_total');
        $free_shipping_total = 100;
        $cart_total = $this->cart->getSubTotal();
        
        return [
            'free_shipping_total' => (float)$free_shipping_total,
            'current_total' => $cart_total,
            'is_free_shipping' => $cart_total >= $free_shipping_total,
            'amount_needed' => $free_shipping_total - $cart_total,
            'amount_needed_formatted' => $this->formatCurrency($free_shipping_total - $cart_total)
        ];
    }

    /**
     * Валидира продукт в количката
     *
     * @param string $cart_id ID на продукта в количката
     * @return array Резултат от валидацията
     */
    public function validateCartProduct($cart_id) {
        $products = $this->cart->getProducts();

        $result = [
            'valid' => false,
            'errors' => [],
            'product' => null
        ];

        foreach ($products as $product) {
            if ($product['cart_id'] === $cart_id) {
                $result['product'] = $product;
                
                // Проверка за наличност
                $product_info = $this->getProduct($product['product_id']);
                if (!$product_info) {
                    $result['errors'][] = 'Продуктът не е намерен';
                    $result['not_found'] = true;
                    return $result;
                }

                // Проверка за статус
                if (!$product_info['status']) {
                    $result['errors'][] = 'Продуктът не е активен';
                    return $result;
                }

                // Проверка за количество
                if ($product_info['subtract'] && $product['quantity'] > $product_info['quantity']) {
                    $result['errors'][] = 'Недостатъчно количество на склад';
                    return $result;
                }

                // Проверка за минимално количество
                if ($product['quantity'] < $product_info['minimum']) {
                    $result['errors'][] = 'Минималното количество е ' . $product_info['minimum'];
                    return $result;
                }

                $result['valid'] = true;
                break;
            }
        }

        if (!$result['product']) {
            $result['errors'][] = 'Продуктът не е намерен в количката';
        }

        return $result;
    }

    /**
     * Получава препоръчани продукти за количката
     *
     * @param int $limit Максимален брой продукти
     * @return array Масив с препоръчани продукти
     */
    public function getRecommendedProducts($limit = 4) {
        $recommended = [];
        $cart_products = $this->cart->getProducts();
        
        if (empty($cart_products)) {
            return $recommended;
        }

        $this->load->model('catalog/product');
        
        // Събираме всички категории от продуктите в количката
        $categories = [];
        foreach ($cart_products as $cart_product) {
            $product_categories = $this->model_catalog_product->getProductCategories($cart_product['product_id']);
            $categories = array_merge($categories, $product_categories);
        }
        
        $categories = array_unique($categories);
        
        if (!empty($categories)) {
            // Вземаме продукти от същите категории
            $category_products = $this->model_catalog_product->getProducts([
                'filter_category_id' => $categories[0],
                'limit' => $limit * 2 // Вземаме повече за филтриране
            ]);
            
            // Филтрираме продуктите, които вече са в количката
            $cart_product_ids = array_column($cart_products, 'product_id');
            
            foreach ($category_products as $product) {
                if (!in_array($product['product_id'], $cart_product_ids) && count($recommended) < $limit) {
                    // Добавяме изображение
                    if (!empty($product['image'])) {
                        try {
                            $this->load->model('tool/image');
                            $product['thumb'] = $this->model_tool_image->resize($product['image'], 200, 200);
                        } catch (\Exception $e) {
                            $product['thumb'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                        }
                    } else {
                        $product['thumb'] = ThemeData()->getImageWebUrl() . 'placeholder.png';
                    }
                    
                    $product['href'] = $this->url->link('product/product', 'product_id=' . $product['product_id']);
                    $recommended[] = $product;
                }
            }
        }

        return $recommended;
    }

    /**
     * Прилага промо код (coupon) към количката
     *
     * @param string $promo_code Промо код
     * @return array Резултат от прилагането
     */
    public function applyPromoCode($promo_code) {
        $result = [
            'success' => false,
            'message' => '',
            'discount_amount' => 0
        ];

        if (empty($promo_code)) {
            $result['message'] = 'Моля въведете промо код';
            return $result;
        }

        // Проверка дали промо кодът вече е приложен
        $existing_coupons = $this->cart_session->get('coupons', []);
        foreach ($existing_coupons as $coupon) {
            if ($coupon['code'] === $promo_code) {
                $result['message'] = 'Този промо код вече е приложен';
                return $result;
            }
        }

        // Валидация на промо кода като coupon
        $coupon_info = $this->validateCoupon($promo_code);

        if (!$coupon_info['valid']) {
            $result['message'] = $coupon_info['message'];
            return $result;
        }

        // Изчисляване на отстъпката
        $discount_amount = $this->calculateCouponDiscount($coupon_info, $this->cart->getProducts());

        // Създаване на coupon обект според OrderSession структурата
        $coupon_data = [
            'coupon_id' => $coupon_info['coupon_id'] ?? 0,
            'name' => $coupon_info['name'] ?? 'Промо код',
            'code' => $promo_code,
            'type' => $coupon_info['type'],
            'discount' => $coupon_info['discount']
        ];

        // Добавяне на новия coupon (заменяме съществуващи)
        $coupons = [$coupon_data];
        $this->cart_session->set('coupons', $coupons);
        $this->cart_session->save();

        // Обновяване на totals
        $this->updateCartTotalsWithCoupon($promo_code, $discount_amount);

        $result['success'] = true;
        $result['message'] = 'Промо кодът беше приложен успешно';
        $result['discount_amount'] = $discount_amount;
        $result['discount_text'] = $this->formatDiscountText($coupon_info['type'], $coupon_info['discount'], $discount_amount);

        return $result;
    }

    /**
     * Прилага ваучер към количката
     *
     * @param string $voucher_code Ваучер код
     * @return array Резултат от прилагането
     */
    public function applyVoucher($voucher_code) {
        $result = [
            'success' => false,
            'message' => '',
            'discount_amount' => 0
        ];

        if (empty($voucher_code)) {
            $result['message'] = 'Моля въведете ваучер код';
            return $result;
        }

        // Проверка дали ваучерът вече е приложен
        $existing_vouchers = $this->cart_session->get('vouchers', []);
        foreach ($existing_vouchers as $voucher) {
            if ($voucher['code'] === $voucher_code) {
                $result['message'] = 'Този ваучер вече е приложен';
                return $result;
            }
        }

        // Валидация на ваучера
        $voucher_info = $this->validateVoucher($voucher_code);
        if (!$voucher_info['valid']) {
            $result['message'] = $voucher_info['message'];
            return $result;
        }

        // Създаване на voucher обект според OrderSession структурата
        $voucher_data = [
            'voucher_id' => $voucher_info['voucher_id'],
            'description' => $voucher_info['description'] ?? 'Ваучер',
            'code' => $voucher_code,
            'from_name' => $voucher_info['from_name'] ?? '',
            'from_email' => $voucher_info['from_email'] ?? '',
            'to_name' => $voucher_info['to_name'] ?? '',
            'to_email' => $voucher_info['to_email'] ?? '',
            'amount' => $voucher_info['amount']
        ];

        // Добавяне на новия voucher (заменяме съществуващи)
        $vouchers = [$voucher_data];
        $this->cart_session->set('vouchers', $vouchers);

        // Обновяване на totals
        $this->updateCartTotalsWithVoucher($voucher_code, $voucher_info['amount']);

        $this->cart_session->save();

        $result['success'] = true;
        $result['message'] = 'Ваучерът беше приложен успешно';
        $result['discount_amount'] = $voucher_info['amount'];
        $result['discount_text'] = $this->formatCurrency($voucher_info['amount']);

        return $result;
    }

    /**
     * Премахва промо код (coupon) от количката
     *
     * @param string $promo_code Промо код за премахване
     * @return array Резултат от премахването
     */
    public function removePromoCode($promo_code) {
        $result = [
            'success' => false,
            'message' => ''
        ];

        if (empty($promo_code)) {
            $result['message'] = 'Невалиден промо код';
            return $result;
        }

        // Проверка дали промо кодът е приложен
        $existing_coupons = $this->cart_session->get('coupons', []);
        $coupon_found = false;

        foreach ($existing_coupons as $coupon) {
            if ($coupon['code'] === $promo_code) {
                $coupon_found = true;
                break;
            }
        }

        if ($coupon_found) {
            // Премахване на промо кода от coupons масива
            $this->cart_session->set('coupons', []);

            // Премахване на coupon тотала
            $this->removeCouponFromTotals();

            $this->cart_session->save();

            $result['success'] = true;
            $result['message'] = 'Промо кодът беше премахнат успешно';
        } else {
            $result['message'] = 'Промо кодът не е намерен';
        }

        return $result;
    }

    /**
     * Премахва ваучер от количката
     *
     * @param string $voucher_code Ваучер код за премахване
     * @return array Резултат от премахването
     */
    public function removeVoucher($voucher_code) {
        $result = [
            'success' => false,
            'message' => ''
        ];

        if (empty($voucher_code)) {
            $result['message'] = 'Невалиден ваучер код';
            return $result;
        }

        // Проверка дали ваучерът е приложен
        $existing_vouchers = $this->cart_session->get('vouchers', []);
        $voucher_found = false;

        foreach ($existing_vouchers as $voucher) {
            if ($voucher['code'] === $voucher_code) {
                $voucher_found = true;
                break;
            }
        }

        if ($voucher_found) {
            // Премахване на ваучера от vouchers масива
            $this->cart_session->set('vouchers', []);

            // Премахване на voucher тотала
            $this->removeVoucherFromTotals();

            $this->cart_session->save();

            $result['success'] = true;
            $result['message'] = 'Ваучерът беше премахнат успешно';
        } else {
            $result['message'] = 'Ваучерът не е намерен';
        }

        return $result;
    }

    /**
     * Изчиства всички отстъпки от количката
     *
     * @return array Резултат от изчистването
     */
    public function clearAllDiscounts() {
        $result = [
            'success' => false,
            'message' => ''
        ];

        try {
            // Премахване на всички coupons и vouchers
            $this->cart_session->set('coupons', []);
            $this->cart_session->set('vouchers', []);

            // Премахване на всички discount totals
            $this->clearAllDiscountTotals();

            $this->cart_session->save();

            $result['success'] = true;
            $result['message'] = 'Всички отстъпки бяха премахнати успешно';
        } catch (\Exception $e) {
            $result['message'] = 'Възникна грешка при изчистването на отстъпките';
        }

        return $result;
    }

    /**
     * Валидира промо код
     *
     * @param string $promo_code Промо код
     * @return array Информация за валидността
     */
    private function validatePromoCode($promo_code) {
        // Примерна логика за валидация - може да се замени с реална проверка в базата данни
        $valid_codes = [
            'WELCOME10' => ['discount_type' => 'percentage', 'discount_value' => 10, 'min_amount' => 50],
            'SAVE20' => ['discount_type' => 'percentage', 'discount_value' => 20, 'min_amount' => 100],
            'FIXED15' => ['discount_type' => 'fixed', 'discount_value' => 15, 'min_amount' => 0]
        ];

        if (!isset($valid_codes[$promo_code])) {
            return [
                'valid' => false,
                'message' => 'Невалиден промо код'
            ];
        }

        $code_info = $valid_codes[$promo_code];
        $cart_total = $this->cart->getSubTotal();

        if ($cart_total < $code_info['min_amount']) {
            return [
                'valid' => false,
                'message' => 'Минималната сума за този промо код е ' . $code_info['min_amount'] . 'лв.'
            ];
        }

        // Изчисляване на отстъпката
        $discount_amount = 0;
        if ($code_info['discount_type'] === 'percentage') {
            $discount_amount = ($cart_total * $code_info['discount_value']) / 100;
        } else {
            $discount_amount = $code_info['discount_value'];
        }

        return [
            'valid' => true,
            'message' => 'Промо кодът е валиден',
            'discount_type' => $code_info['discount_type'],
            'discount_value' => $code_info['discount_value'],
            'discount_amount' => $discount_amount
        ];
    }

    /**
     * Валидира coupon код в базата данни
     *
     * @param string $coupon_code Coupon код
     * @return array Информация за валидността
     */
    private function validateCoupon($coupon_code) {

        // Проверка в базата данни за coupons

        $sql = "
            SELECT * FROM " . DB_PREFIX . "coupon
            WHERE code = '" . $this->db->escape($coupon_code) . "'
            AND status = '1'
            AND (date_start = '0000-00-00' OR date_start < NOW())
            AND (date_end = '0000-00-00' OR date_end > NOW())
        ";


        F()->log->developer($sql, __FILE__, __LINE__);
        $query = $this->db->query($sql);

        F()->log->developer($query->num_rows, __FILE__, __LINE__);

        if (!$query->num_rows) {
            return [
                'valid' => false,
                'message' => 'Невалиден или изтекъл промо код'
            ];
        }

        $coupon_info = $query->row;
        $cart_total = $this->cart->getSubTotal();

        // Проверка за минимална сума
        if ($cart_total < $coupon_info['total']) {
            return [
                'valid' => false,
                'message' => 'Минималната сума за този промо код е ' . number_format($coupon_info['total'], 2) . 'лв.'
            ];
        }

        // Проверка за използвания брой
        if ($coupon_info['uses_total'] > 0) {
            $used_query = $this->db->query("
                SELECT COUNT(*) as total FROM " . DB_PREFIX . "order_total
                WHERE code = 'coupon' AND title LIKE '%" . $this->db->escape($coupon_code) . "%'
            ");

            if ($used_query->row['total'] >= $coupon_info['uses_total']) {
                return [
                    'valid' => false,
                    'message' => 'Този промо код е изчерпан'
                ];
            }
        }

        return [
            'valid' => true,
            'coupon_id' => $coupon_info['coupon_id'],
            'name' => $coupon_info['name'],
            'type' => $coupon_info['type'],
            'discount' => $coupon_info['discount']
        ];
    }

    /**
     * Валидира ваучер
     *
     * @param string $voucher_code Ваучер код
     * @return array Информация за валидността
     */
    private function validateVoucher($voucher_code) {
        // Проверка в базата данни за ваучери
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "voucher
            WHERE code = '" . $this->db->escape($voucher_code) . "'
            AND status = '1'
        ");

        if (!$query->num_rows) {
            return [
                'valid' => false,
                'message' => 'Невалиден ваучер код'
            ];
        }

        $voucher = $query->row;

        // Проверка за минимална сума
        $cart_total = $this->cart->getSubTotal();
        if ($cart_total < $voucher['amount']) {
            return [
                'valid' => false,
                'message' => 'Минималната сума за този ваучер е ' . $voucher['amount'] . 'лв.'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Ваучерът е валиден',
            'amount' => $voucher['amount'],
            'voucher_id' => $voucher['voucher_id'],
            'description' => $voucher['description'] ?? 'Ваучер',
            'from_name' => $voucher['from_name'] ?? '',
            'from_email' => $voucher['from_email'] ?? '',
            'to_name' => $voucher['to_name'] ?? '',
            'to_email' => $voucher['to_email'] ?? ''
        ];
    }

    /**
     * Изчислява отстъпката за coupon
     *
     * @param array $coupon_info Информация за coupon-а
     * @param array $products Продукти в количката
     * @return float Размер на отстъпката
     */
    private function calculateCouponDiscount($coupon_info, $products) {
        $discount_amount = 0;
        $sub_total = 0;

        // Изчисляване на sub_total според типа на coupon-а
        if (!empty($coupon_info['product'])) {
            // Coupon за специфични продукти
            $coupon_products = explode(',', $coupon_info['product']);
            foreach ($products as $product) {
                if (in_array($product['product_id'], $coupon_products)) {
                    $sub_total += $product['total'];
                }
            }
        } else {
            // Coupon за всички продукти
            foreach ($products as $product) {
                $sub_total += $product['total'];
            }
        }

        // Изчисляване на отстъпката според типа
        if ($coupon_info['type'] == 'F') {
            // Фиксирана отстъпка
            $discount_amount = $coupon_info['discount'];
        } else {
            // Процентна отстъпка
            $discount_amount = ($sub_total * $coupon_info['discount']) / 100;
        }

        return $discount_amount;
    }

    /**
     * Обновява totals с coupon отстъпка
     *
     * @param string $coupon_code
     * @param float $discount_amount
     */
    private function updateCartTotalsWithCoupon($coupon_code, $discount_amount) {
        $totals = $this->cart_session->get('totals', []);

        // Премахване на съществуващи coupon totals
        $totals = array_filter($totals, function($total) {
            return $total['code'] !== 'coupon';
        });

        // Добавяне на новия coupon total
        $totals['coupon'] = [
            'code' => 'coupon',
            'title' => 'Купон (' . $coupon_code . ')',
            'value' => -abs($discount_amount),
            'sort_order' => 6
        ];

        $this->cart_session->set('totals', $totals);
        $this->cart_session->save();
    }

    /**
     * Обновява totals с voucher отстъпка
     *
     * @param string $voucher_code
     * @param float $voucher_amount
     */
    private function updateCartTotalsWithVoucher($voucher_code, $voucher_amount) {
        $totals = $this->cart_session->get('totals', []);

        // Премахване на съществуващи voucher totals
        $totals = array_filter($totals, function($total) {
            return $total['code'] !== 'voucher';
        });

        // Добавяне на новия voucher total
        $totals['voucher'] = [
            'code' => 'voucher',
            'title' => 'Ваучер (' . $voucher_code . ')',
            'value' => -abs($voucher_amount),
            'sort_order' => 7
        ];

        $this->cart_session->set('totals', $totals);
        $this->cart_session->save();
    }

    /**
     * Премахва coupon от totals
     *
     */
    private function removeCouponFromTotals() {
        $totals = $this->cart_session->get('totals', []);
        $totals = array_filter($totals, function($total) {
            return $total['code'] !== 'coupon';
        });
        $this->cart_session->set('totals', $totals);
        $this->cart_session->save();
    }

    /**
     * Премахва voucher от totals
     *
     */
    private function removeVoucherFromTotals() {
        $totals = $this->cart_session->get('totals', []);
        $totals = array_filter($totals, function($total) {
            return $total['code'] !== 'voucher';
        });
        $this->cart_session->set('totals', $totals);
        $this->cart_session->save();
    }

    /**
     * Изчиства всички discount totals
     *
     */
    private function clearAllDiscountTotals() {
        $totals = $this->cart_session->get('totals', []);
        $totals = array_filter($totals, function($total) {
            return !in_array($total['code'], ['coupon', 'voucher']);
        });
        $this->cart_session->set('totals', $totals);
        $this->cart_session->save();
    }

    /**
     * Прилага coupons и vouchers от CartSession към OpenCart сесията
     *
     * @param array $products_data
     */
    private function applyCouponsAndVouchersToSession($products_data) {
        // Прилагане на coupons
        $coupons = $this->cart_session->get('coupons', []);
        if (!empty($coupons)) {
            foreach ($coupons as $coupon) {
                // Задаване на coupon в OpenCart сесията за calculateTotals
                $this->session->data['coupon'] = $coupon['code'];
            }
        } else {
            // Премахване на coupon от сесията ако няма активни
            unset($this->session->data['coupon']);
        }

        // Прилагане на vouchers
        $vouchers = $this->cart_session->get('vouchers', []);
        if (!empty($vouchers)) {
            foreach ($vouchers as $voucher) {
                // Задаване на voucher в OpenCart сесията за calculateTotals
                $this->session->data['voucher'] = $voucher['code'];
            }
        } else {
            // Премахване на voucher от сесията ако няма активни
            unset($this->session->data['voucher']);
        }
    }

    /**
     * Получава приложените coupons и vouchers от CartSession
     *
     * @return array Масив с приложените отстъпки
     */
    public function getAppliedDiscounts() {

        $coupons = $this->cart_session->get('coupons', []);
        $vouchers = $this->cart_session->get('vouchers', []);
        $totals = $this->cart_session->get('totals', []);

        // Форматиране на данните за Twig шаблона
        $formatted_coupons = [];
        foreach ($coupons as $coupon) {
            $coupon_value_in_currency = isset($totals['coupon']) ? $totals['coupon']['value'] : 0;
            $formatted_coupons[] = [
                'code' => $coupon['code'],
                'name' => $coupon['name'] ?? 'Промо код',
                'type' => $coupon['type'],
                'discount' => $coupon['discount'],
                'discount_text' => $this->formatDiscountText($coupon['type'], $coupon['discount'], $coupon_value_in_currency)
            ];
        }

        $formatted_vouchers = [];
        foreach ($vouchers as $voucher) {
            $formatted_vouchers[] = [
                'code' => $voucher['code'],
                'description' => $voucher['description'] ?? 'Ваучер',
                'amount' => $voucher['amount'],
                'amount_formatted' => $this->formatCurrency($voucher['amount'])
            ];
        }

        return [
            'coupons' => $formatted_coupons,
            'vouchers' => $formatted_vouchers
        ];
    }

    /**
     * Форматира текста за отстъпка според типа
     *
     * @param string $type Тип на отстъпката (F = фиксирана, P = процентна)
     * @param float $discount Размер на отстъпката
     * @param float $discount_in_currency Размер на отстъпката в валuta
     * @return string Форматиран текст
     */
    private function formatDiscountText($type, $discount, $discount_in_currency=null) {
        if ($type == 'F') {
            return $this->formatCurrency($discount) . ' отстъпка';
        } else {
            return number_format($discount, 0) . '% отстъпка' . ($discount_in_currency ? ' (' . $this->formatCurrency($discount_in_currency) . ')' : '');
        }
    }

}
