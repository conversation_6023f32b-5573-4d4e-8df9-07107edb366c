<?php

namespace Theme25\Frontend\Controller\Common;

class MegaMenu extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/mega_menu');
    }

    public function index() {
        $data = [
            'menu_items' => $this->loadMegaMenuItems(),
            // Може да зададете класове по желание чрез конфиг в бъдеще
            // 'container_class' => 'menu-container mt-4',
            // 'nav_class' => 'flex space-x-8',
        ];

        return $this->renderPartTemplate('common/mega_menu', $data);
    }

    public function getMegaMenuItems() {
        return $this->loadMegaMenuItems();
    }

    /**
     * Зарежда елементите за мега менюто от JSON файл.
     * Връща празен масив при проблем.
     */
    private function loadMegaMenuItems() {
        $json_path = $this->getMegaMenuJsonPath();

        if (!$json_path) {
            return [];
        }

        $json = @file_get_contents($json_path);
        if ($json === false) {
            return [];
        }

        $data = json_decode($json, true);
        if (!is_array($data)) {
            return [];
        }

        // По желание: нормализация на данните, ако ключове липсват
        return $data;
    }

    /**
     * Връща пътя до mega-menu.json.
     */
    private function getMegaMenuJsonPath() {
        $json_file_path = $this->getTemplatePath() . '/common/mega-menu/mega-menu.json';
        if (file_exists($json_file_path)) {
            return $json_file_path;
        }

        return false;
    }
}
