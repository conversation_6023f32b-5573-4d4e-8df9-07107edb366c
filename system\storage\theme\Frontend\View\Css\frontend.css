:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Exo 2', sans-serif;
overflow-x: hidden;
}
.overlay-backdrop {
opacity: 0;
visibility: hidden;
transition: all 0.3s ease-in-out;
}
.menu-container {
position: relative;
}
.mega-menu-container {
position: absolute;
top: 100%;
left: 0;
width: 100%;
z-index: 50;
}
.mega-menu-container > div {
opacity: 0;
visibility: hidden;
transform: translateY(10px);
transition: all 0.3s ease-in-out;
}
.mega-menu-container > div.active {
opacity: 1;
visibility: visible;
transform: translateY(0);
}
.menu-item {
position: relative;
}
.menu-item::after {
content: '';
position: absolute;
bottom: 0;
left: 50%;
width: 0;
height: 2px;
background-color: #9000A7;
transition: all 0.3s ease-in-out;
transform: translateX(-50%);
}
.menu-item:hover::after,
.menu-item.active::after {
width: 100%;
}

/* Активна категория - постоянно подчертана */
.menu-item.active a {
color: #e91e63; /* primary color */
}

.menu-item.active::after {
background-color: #e91e63; /* primary color */
}
.menu-item:hover .mega-menu-container {
opacity: 1;
visibility: visible;
transform: translateY(0);
}
.menu-item:hover ~ .overlay-backdrop {
opacity: 1;
visibility: visible;
}
.submenu-item {
transition: all 0.2s ease-in-out;
}
.submenu-item:hover {
transform: translateX(5px);
color: #9000A7;
}
.category-image {
transition: all 0.3s ease;
}
.category-link:hover .category-image {
transform: scale(1.05);
}
.category-overlay {
transition: all 0.3s ease;
opacity: 0;
}
.category-link:hover .category-overlay {
opacity: 0.1;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
.chat-bubble {
opacity: 0;
visibility: hidden;
transform: scale(0.9);
transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
}
.chat-bubble.active {
opacity: 1;
visibility: visible;
transform: scale(1);
}

.slide-cart {
position: fixed;
top: 0;
right: -400px;
width: 400px;
height: 100vh;
background: white;
box-shadow: -4px 0 16px rgba(0,0,0,0.1);
transition: right 0.3s ease;
z-index: 100;
}
.slide-cart.active {
right: 0;
}
.cart-overlay {
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
margin-left: 0!important;
margin-right: 0!important;
background: rgba(0,0,0,0.5);
opacity: 0;
visibility: hidden;
transition: all 0.3s ease;
z-index: 99;
}
.cart-overlay.active {
opacity: 1;
visibility: visible;
}

.custom-checkbox {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    user-select: none;
    }
    .custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    }
    .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    }
    .custom-checkbox:hover input ~ .checkmark {
    background-color: #f9f9f9;
    }
    .custom-checkbox input:checked ~ .checkmark {
    background-color: #9000A7;
    border-color: #9000A7;
    }
    .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    }
    .custom-checkbox input:checked ~ .checkmark:after {
    display: block;
    }
    .custom-checkbox .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    }

.radio-mark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 50%;
    }

.custom-radio {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    user-select: none;
    }
    .custom-radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    }
    .radio-mark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 50%;
    }
    .custom-radio:hover input ~ .radio-mark {
    background-color: #f9f9f9;
    }
    .custom-radio input:checked ~ .radio-mark {
    background-color: #fff;
    border-color: #9000A7;
    }
    .radio-mark:after {
    content: "";
    position: absolute;
    display: none;
    top: 4px;
    left: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #9000A7;
    }
    .custom-radio input:checked ~ .radio-mark:after {
    display: block;
    }

    .standart-view > .container {
        margin: 4vh auto;
        min-height: 70vh;
    }

/* Product Cart Modal Styles */
#productOptionsModal {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

#productOptionsModal.hidden {
    animation: fadeOut 0.3s ease-out;
}

#productOptionsModal .bg-white {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Option Groups Styling */
.option-group {
    border-bottom: 1px solid #f3f4f6;
    padding-bottom: 1rem;
}

.option-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Custom Radio and Checkbox Styling */
.option-radio,
.option-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: var(--primary-color, #3b82f6);
}

/* Quantity Controls */
#decreaseQty,
#increaseQty {
    transition: all 0.2s ease;
    user-select: none;
}

#decreaseQty:hover,
#increaseQty:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

#decreaseQty:active,
#increaseQty:active {
    transform: scale(0.95);
}

/* Modal Overlay Click Prevention */
#productOptionsModal > div {
    pointer-events: auto;
}

/* Error Message Animation */
#modalErrorMessage {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Modal */
@media (max-width: 640px) {
    #productOptionsModal .max-w-md {
        max-width: calc(100vw - 2rem);
        margin: 1rem;
    }

    #productOptionsModal .max-h-[90vh] {
        max-height: calc(100vh - 2rem);
    }
}

/* Loading State for Buy Buttons */
.buyButton.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.buyButton.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}