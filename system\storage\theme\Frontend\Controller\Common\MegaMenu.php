<?php

namespace Theme25\Frontend\Controller\Common;

class MegaMenu extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/mega_menu');
    }

    public function index() {
        $menu_items = $this->loadMegaMenuItems();
        $active_menu_slug = $this->getActiveMenuSlug($menu_items);

        $data = [
            'menu_items' => $menu_items,
            'active_menu_slug' => $active_menu_slug,
            // Може да зададете класове по желание чрез конфиг в бъдеще
            // 'container_class' => 'menu-container mt-4',
            // 'nav_class' => 'flex space-x-8',
        ];

        return $this->renderPartTemplate('common/mega_menu', $data);
    }

    public function getMegaMenuItems() {
        return $this->loadMegaMenuItems();
    }

    /**
     * Зарежда елементите за мега менюто от JSON файл.
     * Връща празен масив при проблем.
     */
    private function loadMegaMenuItems() {
        $json_path = $this->getMegaMenuJsonPath();

        if (!$json_path) {
            return [];
        }

        $json = @file_get_contents($json_path);
        if ($json === false) {
            return [];
        }

        $data = json_decode($json, true);
        if (!is_array($data)) {
            return [];
        }

        // По желание: нормализация на данните, ако ключове липсват
        return $data;
    }

    /**
     * Връща пътя до mega-menu.json.
     */
    private function getMegaMenuJsonPath() {
        $json_file_path = $this->getTemplatePath() . '/common/mega-menu/mega-menu.json';
        if (file_exists($json_file_path)) {
            return $json_file_path;
        }

        return false;
    }

    /**
     * Определя активната главна категория от мега менюто въз основа на текущия URL
     *
     * @param array $menu_items Елементите на мега менюто
     * @return string|null Slug на активната главна категория или null ако няма активна
     */
    private function getActiveMenuSlug($menu_items) {
        // Проверяваме дали сме в категория страница
        $current_route = $this->requestGet('route');
        if ($current_route !== 'product/category') {
            return null;
        }

        // Получаваме текущата категория от URL
        $current_category_id = $this->getCurrentCategoryId();
        if (!$current_category_id) {
            return null;
        }

        // Зареждаме модела за категории
        $this->loadModelAs('catalog/category', 'categoryModel');

        // Получаваме пълния път на текущата категория
        $category_path = $this->getCategoryPathIds($current_category_id);

        // Търсим коя главна категория от мега менюто съответства на текущата категория
        return $this->findMatchingMenuSlug($menu_items, $category_path, $current_category_id);
    }

    /**
     * Получава ID на текущата категория от URL параметрите
     *
     * @return int|null ID на категорията или null ако не е намерена
     */
    private function getCurrentCategoryId() {
        // OpenCart обичайно подава path=10_20_30 (последната е текущата)
        $path = (string)($this->requestGet('path') ?: '');
        if ($path === '') {
            return (int)$this->requestGet('category_id') ?: null;
        }

        $parts = explode('_', $path);
        $last = end($parts);
        return ctype_digit((string)$last) ? (int)$last : null;
    }

    /**
     * Получава масив с всички ID-та в пътя на категорията (включително родителските)
     *
     * @param int $category_id ID на категорията
     * @return array Масив с ID-та на категориите в пътя
     */
    private function getCategoryPathIds($category_id) {
        $path_ids = [];

        // Получаваме path от URL ако е наличен
        $path = (string)($this->requestGet('path') ?: '');
        if ($path !== '') {
            $parts = explode('_', $path);
            foreach ($parts as $part) {
                if (ctype_digit((string)$part)) {
                    $path_ids[] = (int)$part;
                }
            }
        } else {
            // Ако няма path в URL, добавяме само текущата категория
            $path_ids[] = $category_id;
        }

        return $path_ids;
    }

    /**
     * Търси коя главна категория от мега менюто съответства на дадена категория
     *
     * @param array $menu_items Елементите на мега менюто
     * @param array $category_path_ids Масив с ID-та на категориите в пътя
     * @param int $current_category_id ID на текущата категория
     * @return string|null Slug на съответстващата главна категория
     */
    private function findMatchingMenuSlug($menu_items, $category_path_ids, $current_category_id) {
        foreach ($menu_items as $menu_item) {
            // Проверяваме дали URL-а на главната категория съответства
            if ($this->doesMenuItemMatchCategory($menu_item, $category_path_ids, $current_category_id)) {
                return $menu_item['slug'] ?? $this->generateSlugFromTitle($menu_item['title']);
            }
        }

        return null;
    }

    /**
     * Проверява дали дадена главна категория от менюто съответства на текущата категория
     *
     * @param array $menu_item Елемент от мега менюто
     * @param array $category_path_ids Масив с ID-та на категориите в пътя
     * @param int $current_category_id ID на текущата категория
     * @return bool True ако съответства
     */
    private function doesMenuItemMatchCategory($menu_item, $category_path_ids, $current_category_id) {
        $menu_url = $menu_item['url'] ?? '';

        // Първо проверяваме главната категория
        $menu_category_id = $this->getCategoryIdFromUrl($menu_url);

        if ($menu_category_id) {
            // Проверяваме дали category_id от менюто е в пътя на текущата категория
            if (in_array($menu_category_id, $category_path_ids)) {
                return true;
            }
        }

        // Проверяваме подкатегориите в submenu ако има такива
        if (isset($menu_item['submenu']) && isset($menu_item['submenu']['data'])) {
            if ($this->checkSubmenuMatch($menu_item['submenu'], $category_path_ids, $current_category_id)) {
                return true;
            }
        }

        // Ако не можем да определим category_id, проверяваме по URL съответствие
        return $this->checkUrlMatch($menu_url, $current_category_id);
    }

    /**
     * Проверява дали някоя от подкатегориите в submenu съответства на текущата категория
     *
     * @param array $submenu Submenu данни
     * @param array $category_path_ids Масив с ID-та на категориите в пътя
     * @param int $current_category_id ID на текущата категория
     * @return bool True ако има съответствие
     */
    private function checkSubmenuMatch($submenu, $category_path_ids, $current_category_id) {
        $submenu_data = $submenu['data'] ?? [];

        // За различните типове submenu
        if ($submenu['type'] === 'categories' && is_array($submenu_data)) {
            // Проверяваме всяка колона в categories submenu
            foreach ($submenu_data as $column) {
                if (isset($column['items']) && is_array($column['items'])) {
                    foreach ($column['items'] as $item) {
                        if ($this->checkSubmenuItemMatch($item, $category_path_ids, $current_category_id)) {
                            return true;
                        }
                    }
                }
            }
        } elseif (in_array($submenu['type'], ['gallery', 'promo']) && is_array($submenu_data)) {
            // За gallery и promo типове
            foreach ($submenu_data as $item) {
                if ($this->checkSubmenuItemMatch($item, $category_path_ids, $current_category_id)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Проверява дали един елемент от submenu съответства на текущата категория
     *
     * @param array $item Елемент от submenu
     * @param array $category_path_ids Масив с ID-та на категориите в пътя
     * @param int $current_category_id ID на текущата категория
     * @return bool True ако има съответствие
     */
    private function checkSubmenuItemMatch($item, $category_path_ids, $current_category_id) {
        $item_url = $item['url'] ?? '';

        if (empty($item_url)) {
            return false;
        }

        // Получаваме category_id от URL на елемента
        $item_category_id = $this->getCategoryIdFromUrl($item_url);

        if ($item_category_id) {
            // Проверяваме дали category_id от елемента е в пътя на текущата категория
            return in_array($item_category_id, $category_path_ids);
        }

        // Проверяваме по URL съответствие
        return $this->checkUrlMatch($item_url, $current_category_id);
    }

    /**
     * Извлича category_id от URL адрес
     *
     * @param string $url URL адрес
     * @return int|null ID на категорията или null ако не е намерен
     */
    private function getCategoryIdFromUrl($url) {
        if (empty($url)) {
            return null;
        }

        // Зареждаме модела за SEO URL-и ако е наличен
        if (is_callable([$this, 'loadModelAs'])) {
            try {
                $this->loadModelAs('catalog/seo_url', 'seoUrlModel');

                // Търсим в seo_url таблицата
                $query = $this->db->query("
                    SELECT query
                    FROM " . DB_PREFIX . "seo_url
                    WHERE keyword = '" . $this->db->escape($url) . "'
                    AND store_id = '" . (int)$this->config->get('config_store_id') . "'
                    AND language_id = '" . (int)$this->config->get('config_language_id') . "'
                ");

                if ($query->num_rows) {
                    $query_parts = explode('=', $query->row['query']);
                    if ($query_parts[0] === 'category_id' && isset($query_parts[1])) {
                        return (int)$query_parts[1];
                    }
                }
            } catch (Exception $e) {
                // Ако има грешка, продължаваме без SEO URL проверка
            }
        }

        return null;
    }

    /**
     * Проверява съответствие по URL между менюто и текущата категория
     *
     * @param string $menu_url URL от менюто
     * @param int $current_category_id ID на текущата категория
     * @return bool True ако има съответствие
     */
    private function checkUrlMatch($menu_url, $current_category_id) {
        if (empty($menu_url)) {
            return false;
        }

        // Получаваме информация за текущата категория
        $category_info = $this->categoryModel->getCategory($current_category_id);
        if (!$category_info) {
            return false;
        }

        // Проверяваме дали URL-а от менюто съответства на SEO URL на категорията
        try {
            $query = $this->db->query("
                SELECT keyword
                FROM " . DB_PREFIX . "seo_url
                WHERE query = 'category_id=" . (int)$current_category_id . "'
                AND store_id = '" . (int)$this->config->get('config_store_id') . "'
                AND language_id = '" . (int)$this->config->get('config_language_id') . "'
            ");

            if ($query->num_rows && $query->row['keyword'] === $menu_url) {
                return true;
            }
        } catch (Exception $e) {
            // Ако има грешка, продължаваме без SEO URL проверка
        }

        return false;
    }

    /**
     * Генерира slug от заглавие ако не е зададен
     *
     * @param string $title Заглавие
     * @return string Генериран slug
     */
    private function generateSlugFromTitle($title) {
        return strtolower(str_replace([' ', 'ъ', 'ь'], ['-', 'a', 'y'], $title));
    }
}
