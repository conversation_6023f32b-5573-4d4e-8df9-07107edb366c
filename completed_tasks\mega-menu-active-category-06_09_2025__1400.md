# Модификация на мега менюто за маркиране на активната главна категория

## Промпт (Подканата)
Искам да модифицираш функционалността на мега менюто, така че когато потребителят се намира в дадена категория продукти, главната (родителската) категория в навигационното меню да остава визуално подчертана/активна постоянно, а не само при hover ефект с мишката.

В момента главната категория се подчертава само когато позиционирам мишката над менюто (hover състояние), но искам тя да остава подчертана докато съм в която и да е подкategория от тази главна категория.

## Извършени промени

### 1. Модификация на MegaMenu контролера
**Файл:** `system/storage/theme/Frontend/Controller/Common/MegaMenu.php`
**Резервно копие:** `MegaMenu_2025-09-06_1400.php`

#### Добавени методи:
- `getActiveMenuSlug()` - Определя активната главна категория от мега менюто
- `getCurrentCategoryId()` - Получава ID на текущата категория от URL параметрите
- `getCategoryPathIds()` - Получава масив с всички ID-та в пътя на категорията
- `findMatchingMenuSlug()` - Търси коя главна категория от мега менюто съответства
- `doesMenuItemMatchCategory()` - Проверява съответствие между меню елемент и категория
- `checkSubmenuMatch()` - Проверява подкатегориите в submenu
- `checkSubmenuItemMatch()` - Проверява отделни елементи от submenu
- `getCategoryIdFromUrl()` - Извлича category_id от URL адрес
- `checkUrlMatch()` - Проверява съответствие по URL
- `generateSlugFromTitle()` - Генерира slug от заглавие

#### Модифициран метод:
- `index()` - Добавена логика за определяне на активната категория

### 2. Модификация на Twig шаблона
**Файл:** `system/storage/theme/Frontend/View/Template/common/mega_menu.twig`
**Резервно копие:** `mega_menu_2025-09-06_1400.twig`

#### Промени:
- Добавена логика за определяне на активната категория
- Добавен CSS клас `active` към активната главна категория
- Добавен CSS клас `text-primary active` към линка на активната категория

### 3. Модификация на CSS стиловете
**Файл:** `system/storage/theme/Frontend/View/Css/frontend.css`

#### Добавени стилове:
```css
.menu-item:hover::after,
.menu-item.active::after {
width: 100%;
}

/* Активна категория - постоянно подчертана */
.menu-item.active a {
color: #e91e63; /* primary color */
}

.menu-item.active::after {
background-color: #e91e63; /* primary color */
}
```

## Логика на работа

1. **Определяне на текущата категория:**
   - Проверява се дали сме в категория страница (`route=product/category`)
   - Извлича се category_id от URL параметъра `path` (напр. `path=10_20_30`)
   - Получава се пълният път на категорията включително родителските категории

2. **Мапиране към мега менюто:**
   - Проверява се всяка главна категория от мега менюто
   - Търси се съответствие по category_id чрез SEO URL таблицата
   - Проверяват се и подкатегориите в submenu структурата
   - Връща се slug-а на съответстващата главна категория

3. **Визуално маркиране:**
   - Активната главна категория получава CSS клас `active`
   - Линкът получава primary цвят и клас `active`
   - Подчертаването остава постоянно видимо

## Поддържани типове submenu
- `categories` - Проверява items в колоните
- `gallery` - Проверява директно елементите
- `promo` - Проверява директно елементите

## Тестване
Функционалността е готова за тестване в различни категории и подкатегории за проверка на правилното маркиране на активната главна категория.

## Технически детайли
- Използва се SEO URL таблицата за мапиране между URL адреси и category_id
- Поддържа се както path параметър, така и директен category_id
- Обработват се грешки при липсващи модели или таблици
- Генерира се fallback slug от заглавието ако не е зададен
